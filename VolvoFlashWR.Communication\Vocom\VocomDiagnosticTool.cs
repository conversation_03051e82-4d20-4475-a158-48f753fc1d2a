using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Collections.Generic;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Communication.Vocom
{
    /// <summary>
    /// Diagnostic tool for analyzing Vocom DLL issues
    /// </summary>
    public class VocomDiagnosticTool
    {
        private readonly ILoggingService _logger;

        // Windows API imports for DLL analysis
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool FreeLibrary(IntPtr hModule);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern uint GetLastError();

        [DllImport("imagehlp.dll", SetLastError = true)]
        private static extern bool MapAndLoad(string imageName, string dllPath, out LOADED_IMAGE loadedImage, bool dotDll, bool readOnly);

        [DllImport("imagehlp.dll", SetLastError = true)]
        private static extern bool UnMapAndLoad(ref LOADED_IMAGE loadedImage);

        [StructLayout(LayoutKind.Sequential)]
        private struct LOADED_IMAGE
        {
            public string ModuleName;
            public IntPtr hFile;
            public IntPtr MappedAddress;
            public IntPtr FileHeader;
            public IntPtr LastRvaSection;
            public uint NumberOfSections;
            public IntPtr Sections;
            public uint Characteristics;
            public bool fSystemImage;
            public bool fDOSImage;
            public bool fReadOnly;
            public byte Version;
        }

        public VocomDiagnosticTool(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Runs comprehensive diagnostics on Vocom DLLs
        /// </summary>
        public void RunDiagnostics()
        {
            _logger.LogInformation("=== Starting Vocom DLL Diagnostics ===", "VocomDiagnosticTool");

            // Check APCI library
            DiagnoseApciLibrary();

            // Check WUDFPuma library
            DiagnoseWUDFPumaLibrary();

            // Check system dependencies
            CheckSystemDependencies();

            _logger.LogInformation("=== Vocom DLL Diagnostics Complete ===", "VocomDiagnosticTool");
        }

        /// <summary>
        /// Diagnoses issues with apci.dll
        /// </summary>
        private void DiagnoseApciLibrary()
        {
            _logger.LogInformation("--- Diagnosing APCI Library ---", "VocomDiagnosticTool");

            string apciPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "apci.dll");
            if (!File.Exists(apciPath))
            {
                _logger.LogError($"apci.dll not found at: {apciPath}", "VocomDiagnosticTool");
                return;
            }

            // Check file properties
            FileInfo fileInfo = new FileInfo(apciPath);
            _logger.LogInformation($"APCI file size: {fileInfo.Length} bytes", "VocomDiagnosticTool");
            _logger.LogInformation($"APCI last modified: {fileInfo.LastWriteTime}", "VocomDiagnosticTool");

            // Try to load the library
            IntPtr handle = LoadLibrary(apciPath);
            if (handle == IntPtr.Zero)
            {
                uint error = GetLastError();
                _logger.LogError($"Failed to load apci.dll. Error: {error} (0x{error:X})", "VocomDiagnosticTool");
                return;
            }

            _logger.LogInformation("Successfully loaded apci.dll", "VocomDiagnosticTool");

            // Check for expected functions
            string[] expectedFunctions = {
                "APCI_Initialize",
                "APCI_Shutdown",
                "APCI_DetectDevices",
                "APCI_ConnectDevice",
                "APCI_DisconnectDevice",
                "APCI_SendData",
                "ApciInitialize",
                "ApciShutdown"
            };

            foreach (string funcName in expectedFunctions)
            {
                IntPtr procAddress = GetProcAddress(handle, funcName);
                if (procAddress != IntPtr.Zero)
                {
                    _logger.LogInformation($"✓ Found function: {funcName}", "VocomDiagnosticTool");
                }
                else
                {
                    _logger.LogWarning($"✗ Missing function: {funcName}", "VocomDiagnosticTool");
                }
            }

            FreeLibrary(handle);
        }

        /// <summary>
        /// Diagnoses issues with WUDFPuma.dll
        /// </summary>
        private void DiagnoseWUDFPumaLibrary()
        {
            _logger.LogInformation("--- Diagnosing WUDFPuma Library ---", "VocomDiagnosticTool");

            // Check multiple possible locations
            string[] searchPaths = {
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "WUDFPuma.dll"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "88890020 Adapter", "UMDF", "WUDFPuma.dll"),
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Libraries", "WUDFPuma.dll")
            };

            string foundPath = string.Empty;
            foreach (string path in searchPaths)
            {
                if (File.Exists(path))
                {
                    foundPath = path;
                    _logger.LogInformation($"Found WUDFPuma.dll at: {path}", "VocomDiagnosticTool");
                    break;
                }
            }

            if (string.IsNullOrEmpty(foundPath))
            {
                _logger.LogError("WUDFPuma.dll not found in any search location", "VocomDiagnosticTool");
                return;
            }

            // Check file properties
            FileInfo fileInfo = new FileInfo(foundPath);
            _logger.LogInformation($"WUDFPuma file size: {fileInfo.Length} bytes", "VocomDiagnosticTool");
            _logger.LogInformation($"WUDFPuma last modified: {fileInfo.LastWriteTime}", "VocomDiagnosticTool");

            // Try to load the library
            IntPtr handle = LoadLibrary(foundPath);
            if (handle == IntPtr.Zero)
            {
                uint error = GetLastError();
                _logger.LogError($"Failed to load WUDFPuma.dll. Error: {error} (0x{error:X})", "VocomDiagnosticTool");
                
                // Provide specific error analysis
                AnalyzeLoadError(error);
                return;
            }

            _logger.LogInformation("Successfully loaded WUDFPuma.dll", "VocomDiagnosticTool");

            // Check for expected functions
            string[] expectedFunctions = {
                "Vocom_Initialize",
                "Vocom_Shutdown",
                "Vocom_DetectDevices",
                "Vocom_ConnectDevice",
                "Vocom_DisconnectDevice",
                "Vocom_SendCANFrame"
            };

            foreach (string funcName in expectedFunctions)
            {
                IntPtr procAddress = GetProcAddress(handle, funcName);
                if (procAddress != IntPtr.Zero)
                {
                    _logger.LogInformation($"✓ Found function: {funcName}", "VocomDiagnosticTool");
                }
                else
                {
                    _logger.LogWarning($"✗ Missing function: {funcName}", "VocomDiagnosticTool");
                }
            }

            FreeLibrary(handle);
        }

        /// <summary>
        /// Analyzes specific load errors and provides recommendations
        /// </summary>
        private void AnalyzeLoadError(uint errorCode)
        {
            switch (errorCode)
            {
                case 126: // ERROR_MOD_NOT_FOUND
                    _logger.LogError("Error 126: The specified module could not be found", "VocomDiagnosticTool");
                    _logger.LogError("This usually means missing dependencies. Check Visual C++ Redistributables.", "VocomDiagnosticTool");
                    break;

                case 193: // ERROR_BAD_EXE_FORMAT
                    _logger.LogError("Error 193: The application or DLL is not a valid Windows image", "VocomDiagnosticTool");
                    _logger.LogError("This usually means architecture mismatch (32-bit vs 64-bit).", "VocomDiagnosticTool");
                    break;

                case 998: // ERROR_NOACCESS
                    _logger.LogError("Error 998: Invalid access to memory location", "VocomDiagnosticTool");
                    _logger.LogError("This usually means the DLL is corrupted or incompatible.", "VocomDiagnosticTool");
                    break;

                default:
                    _logger.LogError($"Unknown error code: {errorCode}", "VocomDiagnosticTool");
                    break;
            }
        }

        /// <summary>
        /// Checks for required system dependencies
        /// </summary>
        private void CheckSystemDependencies()
        {
            _logger.LogInformation("--- Checking System Dependencies ---", "VocomDiagnosticTool");

            string[] requiredDependencies = {
                "msvcr120.dll",      // Visual C++ 2013
                "msvcp120.dll",
                "msvcr140.dll",      // Visual C++ 2015-2019
                "msvcp140.dll",
                "vcruntime140.dll",
                "api-ms-win-crt-runtime-l1-1-0.dll" // Universal CRT
            };

            foreach (string dependency in requiredDependencies)
            {
                IntPtr handle = LoadLibrary(dependency);
                if (handle != IntPtr.Zero)
                {
                    _logger.LogInformation($"✓ Available: {dependency}", "VocomDiagnosticTool");
                    FreeLibrary(handle);
                }
                else
                {
                    _logger.LogWarning($"✗ Missing: {dependency}", "VocomDiagnosticTool");
                }
            }
        }

        /// <summary>
        /// Generates a diagnostic report
        /// </summary>
        public void GenerateDiagnosticReport(string outputPath)
        {
            try
            {
                using (StreamWriter writer = new StreamWriter(outputPath))
                {
                    writer.WriteLine("=== Vocom DLL Diagnostic Report ===");
                    writer.WriteLine($"Generated: {DateTime.Now}");
                    writer.WriteLine($"Application: {AppDomain.CurrentDomain.BaseDirectory}");
                    writer.WriteLine();

                    // Add more detailed analysis here
                    writer.WriteLine("For detailed analysis, check the application logs.");
                }

                _logger.LogInformation($"Diagnostic report saved to: {outputPath}", "VocomDiagnosticTool");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to generate diagnostic report: {ex.Message}", "VocomDiagnosticTool");
            }
        }
    }
}
