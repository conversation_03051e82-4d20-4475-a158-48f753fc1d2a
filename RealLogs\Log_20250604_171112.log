Log started at 6/4/2025 5:11:12 PM
2025-06-04 17:11:12.953 [Information] LoggingService: Logging service initialized
2025-06-04 17:11:12.967 [Information] AppConfigurationService: Initializing configuration service
2025-06-04 17:11:12.968 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Config
2025-06-04 17:11:12.968 [Information] AppConfigurationService: Configuration file not found, creating default
2025-06-04 17:11:12.972 [Warning] AppConfigurationService: Configuration service not initialized
2025-06-04 17:11:12.973 [Information] AppConfigurationService: Default configuration created
2025-06-04 17:11:12.974 [Information] AppConfigurationService: Configuration service initialized successfully
2025-06-04 17:11:12.974 [Information] App: Configuration service initialized successfully
2025-06-04 17:11:12.975 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-06-04 17:11:12.976 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: ''
2025-06-04 17:11:12.976 [Information] App: Environment variable exists: False, not 'false': True
2025-06-04 17:11:12.976 [Information] App: Final useDummyImplementations value: False
2025-06-04 17:11:12.977 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: ''
2025-06-04 17:11:12.977 [Information] App: usePatchedImplementation flag is: False
2025-06-04 17:11:12.978 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: ''
2025-06-04 17:11:12.978 [Information] App: APCI_LIBRARY_PATH environment variable is set to: ''
2025-06-04 17:11:12.978 [Information] App: VERBOSE_LOGGING environment variable is set to: ''
2025-06-04 17:11:12.979 [Information] App: verboseLogging flag is: False
2025-06-04 17:11:12.981 [Information] App: Verifying real hardware requirements...
2025-06-04 17:11:12.981 [Warning] App: ✗ Missing critical library: WUDFPuma.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\WUDFPuma.dll
2025-06-04 17:11:12.982 [Warning] App: ✗ Missing critical library: apci.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\apci.dll
2025-06-04 17:11:12.982 [Warning] App: ✗ Missing critical library: Volvo.ApciPlus.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\Volvo.ApciPlus.dll
2025-06-04 17:11:12.982 [Warning] App: ✗ Missing critical library: Volvo.ApciPlusData.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\Volvo.ApciPlusData.dll
2025-06-04 17:11:12.983 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 17:11:12.983 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-06-04 17:11:12.984 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Vocom\config.json
2025-06-04 17:11:12.984 [Warning] App: ⚠ Some real hardware requirements are missing - application may fall back to dummy mode
2025-06-04 17:11:12.997 [Information] App: Creating standard VocomServiceFactory instance
2025-06-04 17:11:12.997 [Information] App: Successfully created standard VocomServiceFactory instance
2025-06-04 17:11:12.998 [Information] App: Using VolvoFlashWR.Communication.Vocom.VocomServiceFactory Vocom service factory
2025-06-04 17:11:12.998 [Information] App: Checking if PTT application is running before creating Vocom service
2025-06-04 17:11:13.046 [Information] App: Creating Vocom service (attempt 1/3)
2025-06-04 17:11:13.049 [Information] VocomServiceFactory: Creating Vocom service with default settings
2025-06-04 17:11:13.050 [Information] VocomServiceFactory: Phoenix Vocom adapter not enabled, skipping
2025-06-04 17:11:13.050 [Information] VocomServiceFactory: Phoenix adapter initialization failed, attempting to create standard Vocom driver
2025-06-04 17:11:13.052 [Information] VocomDriver: Initializing Vocom driver
2025-06-04 17:11:13.054 [Information] VocomNativeInterop: Initializing Vocom driver
2025-06-04 17:11:13.061 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-06-04 17:11:13.062 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 17:11:13.062 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 17:11:13.063 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 17:11:13.065 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-06-04 17:11:13.069 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-06-04 17:11:13.070 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-06-04 17:11:13.073 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-06-04 17:11:13.075 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-06-04 17:11:13.075 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-06-04 17:11:13.076 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-06-04 17:11:13.081 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-06-04 17:11:13.082 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-06-04 17:11:13.084 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-06-04 17:11:13.085 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-06-04 17:11:13.085 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-06-04 17:11:13.086 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-06-04 17:11:13.086 [Information] VocomDriver: Vocom driver initialized successfully
2025-06-04 17:11:13.090 [Information] VocomService: Initializing Vocom service with dependencies
2025-06-04 17:11:13.091 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-06-04 17:11:13.094 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-06-04 17:11:13.096 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-06-04 17:11:13.187 [Information] WiFiCommunicationService: WiFi is available
2025-06-04 17:11:13.188 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-06-04 17:11:13.190 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-06-04 17:11:13.192 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-06-04 17:11:13.194 [Information] BluetoothCommunicationService: Bluetooth is available
2025-06-04 17:11:13.195 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-06-04 17:11:13.197 [Information] VocomService: Initializing Vocom service
2025-06-04 17:11:13.199 [Information] VocomService: Checking if PTT application is running
2025-06-04 17:11:13.217 [Information] VocomService: PTT application is not running
2025-06-04 17:11:13.221 [Information] VocomService: Vocom service initialized successfully
2025-06-04 17:11:13.222 [Information] VocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-06-04 17:11:13.223 [Information] App: Initializing Vocom service
2025-06-04 17:11:13.223 [Information] VocomService: Initializing Vocom service
2025-06-04 17:11:13.223 [Information] VocomService: Checking if PTT application is running
2025-06-04 17:11:13.238 [Information] VocomService: PTT application is not running
2025-06-04 17:11:13.239 [Information] VocomService: Vocom service initialized successfully
2025-06-04 17:11:13.242 [Information] VocomService: Scanning for Vocom devices
2025-06-04 17:11:13.247 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 17:11:13.277 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 17:11:13.281 [Information] VocomService: Found 2 Vocom devices
2025-06-04 17:11:13.282 [Information] App: Found 2 Vocom devices, attempting to connect to the first one
2025-06-04 17:11:13.285 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 17:11:13.285 [Information] VocomService: Checking if PTT application is running
2025-06-04 17:11:13.302 [Information] VocomService: PTT application is not running
2025-06-04 17:11:13.304 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 17:11:13.305 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-06-04 17:11:14.110 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 17:11:14.111 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 17:11:14.112 [Information] App: Connected to Vocom device Vocom - 88890300 (Bluetooth)
2025-06-04 17:11:14.116 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-06-04 17:11:14.120 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:14.121 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-06-04 17:11:14.125 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 17:11:14.127 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 17:11:14.128 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 17:11:14.133 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 17:11:14.135 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 17:11:14.148 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 17:11:14.151 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 17:11:14.153 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 17:11:14.161 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 17:11:14.164 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 17:11:14.176 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 17:11:14.177 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 17:11:14.178 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 17:11:14.178 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 17:11:14.178 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 17:11:14.179 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 17:11:14.179 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 17:11:14.180 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 17:11:14.180 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 17:11:14.186 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 17:11:14.186 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 17:11:14.186 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 17:11:14.187 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 17:11:14.187 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 17:11:14.187 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 17:11:14.187 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 17:11:14.188 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 17:11:14.192 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 17:11:14.196 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.197 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.197 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.198 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.200 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.201 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.204 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.205 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.207 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.208 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-04 17:11:14.210 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-04 17:11:14.211 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 17:11:14.215 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 17:11:14.217 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.217 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.218 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.218 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.218 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.218 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.219 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.219 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.219 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.220 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.220 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.222 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.230 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.231 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.231 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.231 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.232 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.232 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.232 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.233 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.233 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.233 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.234 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.234 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.240 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.241 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.241 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.241 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.242 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.242 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.242 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.243 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.243 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.243 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.244 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.244 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.250 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.251 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.251 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.251 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.251 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.252 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.252 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.252 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.253 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.253 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.253 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.254 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.259 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.268 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.268 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.269 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.269 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.269 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.270 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.270 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.270 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.271 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.271 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.271 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.277 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.277 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.278 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.278 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.278 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.279 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.279 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.279 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.280 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.280 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.280 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.281 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.287 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.287 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.288 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.288 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.288 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.289 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.289 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.289 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.290 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.290 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.290 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.291 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.296 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.297 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.297 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.297 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.298 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.298 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.298 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.299 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.299 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.299 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.300 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.300 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.306 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.307 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.307 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.307 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.308 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.308 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.308 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.309 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.309 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.309 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.310 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.310 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.316 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.316 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.317 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.317 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.317 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.318 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.318 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.318 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.319 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.319 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.319 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.319 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.325 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.326 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.326 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.326 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.326 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.327 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.327 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.327 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.328 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.328 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.328 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.329 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.334 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.334 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.335 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.335 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.335 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.336 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.336 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.336 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.337 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.337 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.337 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.338 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.343 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.343 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.344 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.344 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.344 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.345 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.345 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.345 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.346 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.346 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.346 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.347 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.353 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.353 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.354 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.354 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.354 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.355 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.355 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.355 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.356 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.356 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.356 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.356 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.362 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.362 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.363 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.363 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.363 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.364 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.364 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.364 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.365 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.365 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.365 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.366 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.372 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.372 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.373 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.373 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.373 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.374 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.374 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.374 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.375 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.375 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.375 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.376 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.382 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.382 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.383 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.383 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.383 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.384 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.384 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.384 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.385 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.385 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.385 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.385 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.391 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.392 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.392 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.392 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.393 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.393 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.393 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.394 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.394 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.394 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.395 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.395 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.401 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.401 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.402 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.402 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.402 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.403 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.403 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.403 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.403 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.404 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.404 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.404 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.410 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:14.410 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.411 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.411 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.411 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:14.412 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.412 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.412 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.413 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.413 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.413 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.413 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:14.419 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 17:11:14.420 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-04 17:11:14.424 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 17:11:14.424 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 17:11:14.435 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 17:11:14.436 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 17:11:14.436 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 17:11:14.439 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:14.439 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:14.440 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:14.440 [Information] VocomService: Using generic data transfer
2025-06-04 17:11:14.443 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 17:11:14.444 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 17:11:14.444 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.444 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:14.444 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:14.446 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 17:11:14.447 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:14.448 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 17:11:14.449 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 17:11:14.450 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 17:11:14.451 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 17:11:14.462 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 17:11:14.463 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 17:11:14.464 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 17:11:14.474 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 17:11:14.485 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 17:11:14.496 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 17:11:14.507 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 17:11:14.518 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 17:11:14.520 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 17:11:14.520 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 17:11:14.531 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 17:11:14.532 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 17:11:14.532 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 17:11:14.543 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 17:11:14.554 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 17:11:14.565 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 17:11:14.576 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 17:11:14.587 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 17:11:14.598 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 17:11:14.600 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 17:11:14.600 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 17:11:14.611 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 17:11:14.612 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 17:11:14.613 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 17:11:14.613 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 17:11:14.613 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 17:11:14.613 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 17:11:14.614 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 17:11:14.614 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 17:11:14.614 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 17:11:14.615 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 17:11:14.615 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 17:11:14.615 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 17:11:14.615 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 17:11:14.616 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 17:11:14.616 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 17:11:14.616 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 17:11:14.617 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 17:11:14.717 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 17:11:14.718 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 17:11:14.721 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 17:11:14.723 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:14.723 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 17:11:14.724 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 17:11:14.724 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:14.725 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 17:11:14.725 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 17:11:14.726 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:14.726 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 17:11:14.726 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 17:11:14.727 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:14.727 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 17:11:14.727 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 17:11:14.728 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-06-04 17:11:14.731 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-06-04 17:11:14.733 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-06-04 17:11:14.739 [Information] BackupService: Initializing backup service
2025-06-04 17:11:14.739 [Information] BackupService: Backup service initialized successfully
2025-06-04 17:11:14.739 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-06-04 17:11:14.740 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-06-04 17:11:14.742 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-06-04 17:11:14.806 [Information] BackupService: Compressing backup data
2025-06-04 17:11:14.816 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (451 bytes)
2025-06-04 17:11:14.817 [Information] BackupServiceFactory: Created template for category: Production
2025-06-04 17:11:14.818 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-06-04 17:11:14.819 [Information] BackupService: Compressing backup data
2025-06-04 17:11:14.820 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (449 bytes)
2025-06-04 17:11:14.820 [Information] BackupServiceFactory: Created template for category: Development
2025-06-04 17:11:14.821 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-06-04 17:11:14.821 [Information] BackupService: Compressing backup data
2025-06-04 17:11:14.822 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (447 bytes)
2025-06-04 17:11:14.824 [Information] BackupServiceFactory: Created template for category: Testing
2025-06-04 17:11:14.825 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-06-04 17:11:14.825 [Information] BackupService: Compressing backup data
2025-06-04 17:11:14.826 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (449 bytes)
2025-06-04 17:11:14.827 [Information] BackupServiceFactory: Created template for category: Archived
2025-06-04 17:11:14.828 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-06-04 17:11:14.828 [Information] BackupService: Compressing backup data
2025-06-04 17:11:14.829 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (448 bytes)
2025-06-04 17:11:14.830 [Information] BackupServiceFactory: Created template for category: Critical
2025-06-04 17:11:14.830 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-06-04 17:11:14.831 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-06-04 17:11:14.831 [Information] BackupService: Compressing backup data
2025-06-04 17:11:14.832 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (515 bytes)
2025-06-04 17:11:14.833 [Information] BackupServiceFactory: Created template with predefined tags
2025-06-04 17:11:14.833 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-06-04 17:11:14.835 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-06-04 17:11:14.839 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 17:11:14.841 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 17:11:14.909 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 17:11:14.910 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 17:11:14.911 [Information] BackupSchedulerService: Starting backup scheduler
2025-06-04 17:11:14.912 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-06-04 17:11:14.912 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-06-04 17:11:14.914 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-06-04 17:11:14.914 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-06-04 17:11:14.919 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-06-04 17:11:14.919 [Information] App: Flash operation monitor service initialized successfully
2025-06-04 17:11:14.930 [Information] LicensingService: Initializing licensing service
2025-06-04 17:11:14.976 [Information] LicensingService: License information loaded successfully
2025-06-04 17:11:14.979 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-06-04 17:11:14.979 [Information] App: Licensing service initialized successfully
2025-06-04 17:11:14.979 [Information] App: License status: Trial
2025-06-04 17:11:14.980 [Information] App: Trial period: 30 days remaining
2025-06-04 17:11:14.981 [Information] BackupSchedulerService: Getting all backup schedules
2025-06-04 17:11:15.164 [Information] VocomService: Initializing Vocom service
2025-06-04 17:11:15.164 [Information] VocomService: Checking if PTT application is running
2025-06-04 17:11:15.182 [Information] VocomService: PTT application is not running
2025-06-04 17:11:15.184 [Information] VocomService: Vocom service initialized successfully
2025-06-04 17:11:15.234 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 17:11:15.235 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 17:11:15.235 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 17:11:15.235 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 17:11:15.235 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 17:11:15.237 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 17:11:15.237 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 17:11:15.239 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 17:11:15.239 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 17:11:15.239 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 17:11:15.250 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 17:11:15.251 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 17:11:15.251 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 17:11:15.251 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 17:11:15.252 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 17:11:15.252 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 17:11:15.252 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 17:11:15.253 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 17:11:15.253 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 17:11:15.253 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 17:11:15.253 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 17:11:15.254 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 17:11:15.254 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 17:11:15.254 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 17:11:15.255 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 17:11:15.255 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 17:11:15.255 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 17:11:15.255 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 17:11:15.256 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.256 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.256 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.256 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.257 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.257 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.259 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.259 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.260 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.260 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-04 17:11:15.261 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-04 17:11:15.261 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 17:11:15.261 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 17:11:15.262 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.262 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.262 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.262 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.263 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.263 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.263 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.264 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.264 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.264 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.265 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.265 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.271 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.271 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.272 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.272 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.272 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.272 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.273 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.273 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.273 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.274 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.274 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.275 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.281 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.282 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.282 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.282 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.282 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.283 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.283 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.283 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.284 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.284 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.284 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.285 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.291 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.291 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.292 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.292 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.293 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.293 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.293 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.293 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.294 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.294 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.295 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.295 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.301 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.301 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.302 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.302 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.302 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.303 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.303 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.303 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.304 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.304 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.304 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.305 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.311 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.311 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.312 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.312 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.312 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.313 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.313 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.313 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.314 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.314 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.315 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.315 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.321 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.321 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.322 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.322 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.322 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.323 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.323 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.323 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.324 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.324 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.324 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.325 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.331 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.331 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.332 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.332 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.332 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.333 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.333 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.333 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.334 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.334 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.335 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.335 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.341 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.342 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.342 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.342 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.343 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.343 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.343 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.343 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.344 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.344 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.345 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.345 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.351 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.351 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.352 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.352 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.352 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.353 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.353 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.353 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.353 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.354 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.354 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.355 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.361 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.361 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.362 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.362 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.362 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.362 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.363 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.363 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.363 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.364 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.364 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.365 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.371 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.371 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.372 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.372 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.372 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.373 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.373 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.373 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.374 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.374 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.375 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.375 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.381 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.381 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.382 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.382 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.382 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.383 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.383 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.383 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.384 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.384 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.384 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.385 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.391 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.391 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.392 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.392 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.392 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.393 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.393 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.393 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.394 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.394 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.395 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.395 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.401 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.401 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.402 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.402 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.402 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.403 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.403 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.403 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.403 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.404 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.404 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.405 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.411 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.411 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.412 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.412 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.412 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.412 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.413 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.413 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.413 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.414 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.414 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.415 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.421 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.421 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.422 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.422 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.422 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.422 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.423 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.423 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.423 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.424 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.424 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.424 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.431 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.431 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.432 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.432 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.432 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.433 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.433 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.434 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.434 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.435 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.435 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.435 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.442 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.442 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.443 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.443 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.443 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.444 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.444 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.444 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.444 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.445 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.446 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.446 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.452 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:15.452 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.453 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.453 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.453 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:15.454 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.454 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.454 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.455 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.455 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.455 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.456 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:15.462 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 17:11:15.463 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-04 17:11:15.463 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 17:11:15.463 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 17:11:15.474 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 17:11:15.474 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 17:11:15.475 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 17:11:15.475 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:15.476 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:15.476 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:15.476 [Information] VocomService: Using generic data transfer
2025-06-04 17:11:15.476 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 17:11:15.477 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 17:11:15.477 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.477 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:15.478 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:15.478 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 17:11:15.479 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:15.479 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 17:11:15.479 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 17:11:15.480 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 17:11:15.480 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 17:11:15.491 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 17:11:15.492 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 17:11:15.492 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 17:11:15.502 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 17:11:15.513 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 17:11:15.524 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 17:11:15.535 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 17:11:15.546 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 17:11:15.547 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 17:11:15.547 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 17:11:15.558 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 17:11:15.559 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 17:11:15.559 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 17:11:15.570 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 17:11:15.581 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 17:11:15.592 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 17:11:15.603 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 17:11:15.614 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 17:11:15.625 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 17:11:15.626 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 17:11:15.626 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 17:11:15.637 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 17:11:15.638 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 17:11:15.638 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 17:11:15.638 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 17:11:15.639 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 17:11:15.639 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 17:11:15.639 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 17:11:15.639 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 17:11:15.640 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 17:11:15.640 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 17:11:15.640 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 17:11:15.641 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 17:11:15.641 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 17:11:15.641 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 17:11:15.641 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 17:11:15.642 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 17:11:15.642 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 17:11:15.743 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 17:11:15.743 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 17:11:15.744 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 17:11:15.744 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:15.744 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 17:11:15.745 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 17:11:15.745 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:15.745 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 17:11:15.746 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 17:11:15.746 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:15.746 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 17:11:15.746 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 17:11:15.747 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:15.747 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 17:11:15.747 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 17:11:15.798 [Information] BackupService: Initializing backup service
2025-06-04 17:11:15.799 [Information] BackupService: Backup service initialized successfully
2025-06-04 17:11:15.850 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 17:11:15.851 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 17:11:15.852 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 17:11:15.853 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 17:11:15.904 [Information] BackupService: Getting predefined backup categories
2025-06-04 17:11:15.976 [Information] MainViewModel: Services initialized successfully
2025-06-04 17:11:15.980 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 17:11:15.981 [Information] VocomService: Scanning for Vocom devices
2025-06-04 17:11:15.982 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 17:11:15.983 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 17:11:15.985 [Information] VocomService: Found 2 Vocom devices
2025-06-04 17:11:15.986 [Information] MainViewModel: Found 2 Vocom device(s)
