@echo off
echo ========================================
echo Visual C++ Redistributables Installer
echo ========================================
echo.
echo This script will help you install the missing Visual C++ runtime libraries
echo required for proper Vocom driver functionality.
echo.
echo Missing libraries detected:
echo - msvcr120.dll, msvcp120.dll (Visual C++ 2013)
echo - msvcr140.dll, msvcp140.dll, vcruntime140.dll (Visual C++ 2015-2022)
echo.

echo Opening download pages for Visual C++ Redistributables...
echo.

echo 1. Opening Visual C++ 2013 Redistributable (x64) download page...
start "" "https://www.microsoft.com/en-us/download/details.aspx?id=40784"

timeout /t 3 /nobreak >nul

echo 2. Opening Visual C++ 2015-2022 Redistributable (x64) download page...
start "" "https://aka.ms/vs/17/release/vc_redist.x64.exe"

echo.
echo ========================================
echo INSTALLATION INSTRUCTIONS:
echo ========================================
echo.
echo 1. Download and install Visual C++ 2013 Redistributable (x64)
echo    - File: vcredist_x64.exe
echo    - This provides: msvcr120.dll, msvcp120.dll
echo.
echo 2. Download and install Visual C++ 2015-2022 Redistributable (x64)
echo    - File: vc_redist.x64.exe
echo    - This provides: msvcr140.dll, msvcp140.dll, vcruntime140.dll
echo.
echo 3. After installation, restart your computer
echo.
echo 4. Run the Diagnose_System.bat script to verify installation
echo.
echo 5. Try connecting to your Vocom adapter again
echo.
echo ========================================
echo IMPORTANT NOTES:
echo ========================================
echo.
echo - These redistributables are required for proper Vocom driver operation
echo - Without them, some driver functionality may not work correctly
echo - The application will still load but may have reduced functionality
echo.
echo After installing the redistributables, ensure you have:
echo 1. A real Vocom 1 adapter connected via USB
echo 2. The adapter recognized in Device Manager
echo 3. No other applications using the adapter (close PTT if running)
echo.

pause
