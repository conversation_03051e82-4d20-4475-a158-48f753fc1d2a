@echo off
echo ========================================
echo VolvoFlashWR System Requirements Check
echo ========================================
echo.
echo Checking system requirements for real hardware...
echo.

REM Check for .NET 8.0
echo Checking .NET 8.0 installation...
dotnet --version | findstr "8." >nul
if %ERRORLEVEL% equ 0 (
    echo ✓ .NET 8.0 is installed
) else (
    echo ✗ .NET 8.0 is NOT installed
    echo Please install .NET 8.0 Runtime from: https://dotnet.microsoft.com/download/dotnet/8.0
)
echo.

REM Check for Vocom driver
echo Checking Vocom driver installation...
if exist "C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll" (
    echo ✓ Vocom driver is installed
) else (
    echo ✗ Vocom driver is NOT installed
    echo Please install the Vocom 1 adapter driver (CommunicationUnitInstaller-2.5.0.0.msi)
)
echo.

REM Check for Phoenix Diag (optional)
echo Checking Phoenix Diag installation (optional)...
if exist "C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021" (
    echo ✓ Phoenix Diag is installed
) else (
    echo ⚠ Phoenix Diag is not installed (optional for advanced features)
)
echo.

echo System check completed
echo.
pause
