@echo off
echo ========================================
echo VolvoFlashWR Real Hardware Mode
echo ========================================
echo.
echo Starting application for real Vocom hardware testing...
echo.

REM Change to application directory
cd /d "%~dp0Application"

REM Start the application
if exist "VolvoFlashWR.Launcher.exe" (
    echo Starting VolvoFlashWR for real hardware...
    start "" "VolvoFlashWR.Launcher.exe" --mode=normal --hardware=real
    echo Application started successfully
    echo Check the application window for connection status.
) else (
    echo ERROR: VolvoFlashWR.Launcher.exe not found
    echo Please ensure the export was completed successfully.
    pause
)

echo.
echo Press any key to exit...
pause >nul
