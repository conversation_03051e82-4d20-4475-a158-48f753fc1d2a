using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Collections.Generic;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Communication.Vocom
{
    /// <summary>
    /// Handles WUDFPuma.dll loading with dependency resolution
    /// </summary>
    public class WUDFPumaDependencyResolver
    {
        private readonly ILoggingService _logger;
        private IntPtr _wudfPumaDllHandle = IntPtr.Zero;
        private readonly List<IntPtr> _loadedDependencies = new();

        // Windows API imports for dynamic loading
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool FreeLibrary(IntPtr hModule);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern uint GetLastError();

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool SetDllDirectory(string lpPathName);

        public WUDFPumaDependencyResolver(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Attempts to load WUDFPuma.dll with dependency resolution
        /// </summary>
        public bool LoadWUDFPumaWithDependencies(string wudfPumaPath)
        {
            try
            {
                _logger.LogInformation($"Attempting to load WUDFPuma.dll from: {wudfPumaPath}", "WUDFPumaDependencyResolver");

                if (!File.Exists(wudfPumaPath))
                {
                    _logger.LogError($"WUDFPuma.dll not found at: {wudfPumaPath}", "WUDFPumaDependencyResolver");
                    return false;
                }

                // Set DLL directory to help with dependency resolution
                string dllDirectory = Path.GetDirectoryName(wudfPumaPath) ?? string.Empty;
                if (!string.IsNullOrEmpty(dllDirectory))
                {
                    SetDllDirectory(dllDirectory);
                    _logger.LogInformation($"Set DLL directory to: {dllDirectory}", "WUDFPumaDependencyResolver");
                }

                // Load common dependencies first
                LoadCommonDependencies(dllDirectory);

                // Try to load WUDFPuma.dll
                _wudfPumaDllHandle = LoadLibrary(wudfPumaPath);
                if (_wudfPumaDllHandle == IntPtr.Zero)
                {
                    uint error = GetLastError();
                    _logger.LogError($"Failed to load WUDFPuma.dll. Error code: {error} (0x{error:X})", "WUDFPumaDependencyResolver");
                    
                    // Try to provide more specific error information
                    string errorMessage = GetErrorMessage(error);
                    _logger.LogError($"Error details: {errorMessage}", "WUDFPumaDependencyResolver");
                    
                    return false;
                }

                _logger.LogInformation("Successfully loaded WUDFPuma.dll", "WUDFPumaDependencyResolver");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception loading WUDFPuma.dll: {ex.Message}", "WUDFPumaDependencyResolver");
                return false;
            }
        }

        /// <summary>
        /// Loads common dependencies that WUDFPuma.dll might need
        /// </summary>
        private void LoadCommonDependencies(string baseDirectory)
        {
            string[] commonDependencies = {
                "msvcr120.dll",      // Visual C++ 2013 Runtime
                "msvcp120.dll",      // Visual C++ 2013 Runtime
                "msvcr140.dll",      // Visual C++ 2015-2019 Runtime
                "msvcp140.dll",      // Visual C++ 2015-2019 Runtime
                "vcruntime140.dll",  // Visual C++ 2015-2019 Runtime
                "api-ms-win-crt-runtime-l1-1-0.dll", // Universal CRT
                "WdfCoInstaller01009.dll",  // Windows Driver Framework
                "WUDFUpdate_01009.dll",     // WUDF Update
                "winusbcoinstaller2.dll"    // WinUSB
            };

            foreach (string dependency in commonDependencies)
            {
                LoadDependency(dependency, baseDirectory);
            }
        }

        /// <summary>
        /// Attempts to load a specific dependency
        /// </summary>
        private void LoadDependency(string dependencyName, string baseDirectory)
        {
            try
            {
                // Try different locations for the dependency
                string[] searchPaths = {
                    Path.Combine(baseDirectory, dependencyName),
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, dependencyName),
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Libraries", dependencyName),
                    Path.Combine(Environment.SystemDirectory, dependencyName),
                    dependencyName // Let Windows search in standard locations
                };

                foreach (string path in searchPaths)
                {
                    IntPtr handle = LoadLibrary(path);
                    if (handle != IntPtr.Zero)
                    {
                        _loadedDependencies.Add(handle);
                        _logger.LogInformation($"Loaded dependency: {dependencyName} from {path}", "WUDFPumaDependencyResolver");
                        return;
                    }
                }

                _logger.LogWarning($"Could not load dependency: {dependencyName}", "WUDFPumaDependencyResolver");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Exception loading dependency {dependencyName}: {ex.Message}", "WUDFPumaDependencyResolver");
            }
        }

        /// <summary>
        /// Gets a human-readable error message for a Windows error code
        /// </summary>
        private string GetErrorMessage(uint errorCode)
        {
            return errorCode switch
            {
                126 => "The specified module could not be found (ERROR_MOD_NOT_FOUND)",
                127 => "The specified procedure could not be found (ERROR_PROC_NOT_FOUND)",
                193 => "The application or DLL is not a valid Windows image (ERROR_BAD_EXE_FORMAT)",
                998 => "Invalid access to memory location (ERROR_NOACCESS)",
                _ => $"Unknown error code: {errorCode}"
            };
        }

        /// <summary>
        /// Gets a function pointer from the loaded WUDFPuma.dll
        /// </summary>
        public IntPtr GetProcAddress(string functionName)
        {
            if (_wudfPumaDllHandle == IntPtr.Zero)
            {
                _logger.LogError("WUDFPuma.dll not loaded", "WUDFPumaDependencyResolver");
                return IntPtr.Zero;
            }

            IntPtr procAddress = GetProcAddress(_wudfPumaDllHandle, functionName);
            if (procAddress == IntPtr.Zero)
            {
                _logger.LogWarning($"Function {functionName} not found in WUDFPuma.dll", "WUDFPumaDependencyResolver");
            }
            else
            {
                _logger.LogInformation($"Found function {functionName} in WUDFPuma.dll", "WUDFPumaDependencyResolver");
            }

            return procAddress;
        }

        /// <summary>
        /// Unloads WUDFPuma.dll and all dependencies
        /// </summary>
        public void Unload()
        {
            try
            {
                if (_wudfPumaDllHandle != IntPtr.Zero)
                {
                    FreeLibrary(_wudfPumaDllHandle);
                    _wudfPumaDllHandle = IntPtr.Zero;
                    _logger.LogInformation("Unloaded WUDFPuma.dll", "WUDFPumaDependencyResolver");
                }

                foreach (IntPtr handle in _loadedDependencies)
                {
                    FreeLibrary(handle);
                }
                _loadedDependencies.Clear();

                _logger.LogInformation("Unloaded all dependencies", "WUDFPumaDependencyResolver");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception unloading libraries: {ex.Message}", "WUDFPumaDependencyResolver");
            }
        }

        /// <summary>
        /// Checks if WUDFPuma.dll is loaded
        /// </summary>
        public bool IsLoaded => _wudfPumaDllHandle != IntPtr.Zero;
    }
}
