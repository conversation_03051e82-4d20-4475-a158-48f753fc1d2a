[{"Id": "e035f3b9-8e95-4db7-884b-21b15affe96f", "Name": "Daily Backup - EMS", "Description": "Automatic daily backup at 3:00 AM", "ECUId": "a4af7063-691b-450d-adda-fe9655f8f619", "ECUName": "EMS", "IsEnabled": true, "FrequencyType": 1, "Frequency": 1, "Interval": 1, "TimeOfDay": "03:00:00", "StartHour": 3, "StartMinute": 0, "DaysOfWeek": [1], "StartDayOfWeek": 1, "DayOfMonth": 1, "StartDate": "2025-06-03T00:00:00+03:00", "EndDate": null, "LastExecutionTime": null, "NextExecutionTime": "2025-06-04T03:00:00+03:00", "Category": "Automated", "Tags": ["Daily", "Automated"], "IncludeEEPROM": true, "IncludeMicrocontrollerCode": true, "IncludeParameters": true, "MaxBackupsToKeep": 7, "MaxBackupAge": 30, "RetryCount": 3, "CustomIntervalDays": 1, "ScheduleType": "Standard", "CreatedBackupIds": []}, {"Id": "1e449f39-65ad-45d9-8f62-152824bb2e20", "Name": "Weekly Backup - EMS", "Description": "Automatic weekly backup on Sunday at 4:00 AM", "ECUId": "a4af7063-691b-450d-adda-fe9655f8f619", "ECUName": "EMS", "IsEnabled": true, "FrequencyType": 2, "Frequency": 2, "Interval": 1, "TimeOfDay": "04:00:00", "StartHour": 4, "StartMinute": 0, "DaysOfWeek": [0], "StartDayOfWeek": 1, "DayOfMonth": 1, "StartDate": "2025-06-03T00:00:00+03:00", "EndDate": null, "LastExecutionTime": null, "NextExecutionTime": "2025-06-08T04:00:00+03:00", "Category": "Automated", "Tags": ["Weekly", "Automated"], "IncludeEEPROM": true, "IncludeMicrocontrollerCode": true, "IncludeParameters": true, "MaxBackupsToKeep": 4, "MaxBackupAge": 30, "RetryCount": 3, "CustomIntervalDays": 1, "ScheduleType": "Standard", "CreatedBackupIds": []}]