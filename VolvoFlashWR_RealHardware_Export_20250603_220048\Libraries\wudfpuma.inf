;
; WUDFPuma.inf - Install the OSR USB user-mode driver
;
; (c) Copyright 2011 Movimento Group.
;

[Version]
Signature="$Windows NT$"
Class=%MovimentoClassName%
ClassGuid={DF29BF85-37D6-4729-8B36-F562995D6942}
Provider=%MFGNAME%

; Note: the syntax of DriverVer is 
;  DriverVer=mm/dd/yyyy[,w.x.y.z]
; where both the month and day must be two-digit numbers.
DriverVer=01/12/2017,6.1.7600.16385
CatalogFile=WUDFPuma.cat

[Manufacturer]
%MFGNAME%=Movimento,NTAMD64

[Movimento.NTAMD64]
%Movimento.PumaDeviceDesc%=OsrUsb_Install,            USB\VID_178E&PID_0088&REV_0000
%Movimento.PumaDeviceDesc%=OsrUsb_Install,            USB\VID_178E&PID_0088
%Movimento.88890020DeviceDesc%=OsrUsb_Install,        USB\VID_178E&PID_0020&REV_0001
%Movimento.88890020DeviceDesc%=OsrUsb_Install,        USB\VID_178E&PID_0020&REV_0000
%Movimento.88890020DeviceDesc%=OsrUsb_Install,        USB\VID_178E&PID_0020
%Movimento.88840133DeviceDesc%=OsrUsb_Install,        USB\VID_178E&PID_0021&REV_0000
%Movimento.88840133DeviceDesc%=OsrUsb_Install,        USB\VID_178E&PID_0021
%Movimento.NAVCoMDeviceDesc%=OsrUsb_Install,          USB\VID_178E&PID_0022&REV_0001
%Movimento.NavLinkDeviceDesc%=OsrUsb_Install,         USB\VID_178E&PID_0023&REV_0001
%Movimento.88890300DeviceDesc%=OsrUsb_Install,        USB\VID_178E&PID_0024
%Movimento.Puma2DeviceDesc%=OsrUsb_Install,           USB\VID_178E&PID_0025
%Movimento.Puma3DeviceDesc%=OsrUsb_Install,           USB\VID_178E&PID_0026
%Movimento.Puma4DeviceDesc%=OsrUsb_Install,           USB\VID_178E&PID_0027
%Movimento.PumaFboxDeviceDesc%=OsrUsb_Install,        USB\VID_178E&PID_0028
%Movimento.VenturoDeviceDesc%=OsrUsb_Install,         USB\VID_178E&PID_0029

[ClassInstall32]
AddReg=MovimentoClassAddReg

[MovimentoClassAddReg]
HKR,,,,%MovimentoClassName%
HKR,,Icon,,"-6"

[SourceDisksFiles]
WUDFPuma.dll=1
WudfUpdate_01009.dll=1
WdfCoInstaller01009.dll=1
WinUsbCoinstaller2.dll=1

[SourceDisksNames]
1 = %MediaDescription%

; =================== UMDF OsrUsb Device ==================================

[OsrUsb_Install.NT]
CopyFiles=UMDriverCopy
Include=WINUSB.INF                      ; Import sections from WINUSB.INF
Needs=WINUSB.NT                         ; Run the CopyFiles & AddReg directives for WinUsb.INF

[OsrUsb_Install.NT.hw]
AddReg=OsrUsb_Device_AddReg

[OsrUsb_Install.NT.Services]
AddService=WUDFRd,0x000001fa,WUDFRD_ServiceInstall  ; flag 0x2 sets this as the service for the device
AddService=WinUsb,0x000001f8,WinUsb_ServiceInstall  ; this service is installed because its a filter.

[OsrUsb_Install.NT.Wdf]
KmdfService=WINUSB, WinUsb_Install
UmdfDispatcher=WinUsb
UmdfService=WUDFPuma, WUDFPuma_Install
UmdfServiceOrder=WUDFPuma
UmdfImpersonationLevel=Delegation

[OsrUsb_Install.NT.CoInstallers]
AddReg=CoInstallers_AddReg
CopyFiles=CoInstallers_CopyFiles

[WinUsb_Install]
KmdfLibraryVersion = 1.9

[WUDFPuma_Install]
UmdfLibraryVersion=1.9.0
DriverCLSID = "{22A78171-0CC1-4E82-87AF-D14EF75006CC}"
ServiceBinary = %12%\UMDF\WUDFPuma.dll

[OsrUsb_Device_AddReg]
HKR,,"LowerFilters",0x00010008,"WinUsb" ; FLG_ADDREG_TYPE_MULTI_SZ | FLG_ADDREG_APPEND

[WUDFRD_ServiceInstall]
DisplayName = %WUDFRd_SvcDesc%
ServiceType = 1
StartType = 3
ErrorControl = 1
ServiceBinary = %12%\WUDFRd.sys
LoadOrderGroup = Base

[WinUsb_ServiceInstall]
DisplayName     = %WinUsb_SvcDesc%
ServiceType     = 1
StartType       = 3
ErrorControl    = 1
ServiceBinary   = %12%\WinUSB.sys

[CoInstallers_AddReg]
HKR,,CoInstallers32,0x00010000,"WudfUpdate_01009.dll", "WinUsbCoinstaller2.dll", "WdfCoInstaller01009.dll,WdfCoInstaller"

[CoInstallers_CopyFiles]
WudfUpdate_01009.dll
WdfCoInstaller01009.dll
WinUsbCoinstaller2.dll

[DestinationDirs]
UMDriverCopy=12,UMDF ; copy to driversMdf
CoInstallers_CopyFiles=11

[UMDriverCopy]
WUDFPuma.dll

; =================== Generic ==================================

[Strings]
MFGNAME="Movimento Group"
Movimento.88890020DeviceDesc = "88890020 Unit"
Movimento.PumaDeviceDesc = "Movimento Puma Adapter"
Movimento.Puma2DeviceDesc = "Movimento Puma2 Adapter"
Movimento.Puma3DeviceDesc = "Movimento Puma3 Adapter"
Movimento.Puma4DeviceDesc = "Movimento Puma4 Adapter"
Movimento.PumaFboxDeviceDesc = "Movimento PumaFbox Adapter"
Movimento.VenturoDeviceDesc = "Movimento Venturo Adapter"
Movimento.88840133DeviceDesc = "88840133 Unit"
Movimento.NAVCoMDeviceDesc = "NAVCoM Adapter"
Movimento.NavLinkDeviceDesc = "NavLink Adapter"
Movimento.88890300DeviceDesc = "Vocom - 88890300"
MediaDescription="Installation Disc for Movimento USB adapters"
MovimentoClassName="Movimento USB Adapters"
MovimentoDisplayName="Movimento - USB device driver"
WinUsb_SvcDesc="WinUSB Driver"
WUDFRd_SvcDesc="Windows Driver Foundation - User-mode Driver Framework Reflector"
