@echo off
echo ========================================
echo VolvoFlashWR System Diagnostics
echo ========================================
echo.

REM Change to application directory
cd /d "%~dp0..\Application"

echo Checking VolvoFlashWR Application Files...
echo.

REM Check main executables
if exist "VolvoFlashWR.Launcher.exe" (
    echo ✓ VolvoFlashWR.Launcher.exe found
) else (
    echo ✗ VolvoFlashWR.Launcher.exe missing - CRITICAL
)

if exist "VolvoFlashWR.UI.exe" (
    echo ✓ VolvoFlashWR.UI.exe found
) else (
    echo ✗ VolvoFlashWR.UI.exe missing - CRITICAL
)

echo.
echo Checking Critical Libraries...
echo.

REM Check critical libraries
if exist "Libraries\WUDFPuma.dll" (
    echo ✓ WUDFPuma.dll found
) else (
    echo ✗ WUDFPuma.dll missing - CRITICAL
)

if exist "Libraries\apci.dll" (
    echo ✓ apci.dll found
) else (
    echo ✗ apci.dll missing - CRITICAL
)

if exist "Libraries\Volvo.ApciPlus.dll" (
    echo ✓ Volvo.ApciPlus.dll found
) else (
    echo ✗ Volvo.ApciPlus.dll missing - CRITICAL
)

if exist "Libraries\Volvo.ApciPlusData.dll" (
    echo ✓ Volvo.ApciPlusData.dll found
) else (
    echo ✗ Volvo.ApciPlusData.dll missing - CRITICAL
)

if exist "Libraries\apcidb.dll" (
    echo ✓ apcidb.dll found
) else (
    echo ✗ apcidb.dll missing - IMPORTANT
)

echo.
echo Checking System Vocom Drivers...
echo.

REM Check system Vocom drivers
if exist "C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll" (
    echo ✓ System Vocom driver found
) else (
    echo ✗ System Vocom driver missing - Install CommunicationUnitInstaller-*******.msi
)

if exist "C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021" (
    echo ✓ Phoenix Diag installation found
) else (
    echo ⚠ Phoenix Diag not found - Optional but recommended
)

echo.
echo Checking Configuration Files...
echo.

if exist "Drivers\Vocom\config.json" (
    echo ✓ Vocom configuration found
) else (
    echo ✗ Vocom configuration missing
)

if exist "Config" (
    echo ✓ Config directory found
) else (
    echo ✗ Config directory missing
)

echo.
echo Checking USB Devices (looking for Vocom adapters)...
echo.

REM Use PowerShell to check for USB devices
powershell -Command "Get-WmiObject -Class Win32_USBControllerDevice | ForEach-Object { [wmi]($_.Dependent) } | Where-Object { $_.Description -like '*Vocom*' -or $_.Description -like '*88890*' -or $_.Description -like '*Volvo*' } | Select-Object Description, DeviceID | Format-Table -AutoSize"

if %ERRORLEVEL% NEQ 0 (
    echo No Vocom USB devices detected
    echo.
    echo To check manually:
    echo 1. Open Device Manager
    echo 2. Look for "88890020 Adapter" or similar Vocom device
    echo 3. Ensure no warning symbols are present
)

echo.
echo ========================================
echo DIAGNOSTIC SUMMARY
echo ========================================
echo.
echo If all critical items show ✓, the application should work in real hardware mode.
echo If any critical items show ✗, those issues must be resolved first.
echo.
echo REQUIREMENTS FOR REAL HARDWARE MODE:
echo 1. All critical libraries must be present (✓)
echo 2. System Vocom drivers must be installed (✓)
echo 3. Real Vocom 1 adapter must be connected via USB
echo 4. Real ECU must be connected and powered
echo 5. No other applications should be using the Vocom adapter
echo.
echo If the application still runs in "dummy mode" after fixing all issues,
echo the problem is likely with the hardware setup (no real ECU connected).
echo.
echo Check the REAL_HARDWARE_TROUBLESHOOTING.md file for detailed guidance.
echo.

pause
