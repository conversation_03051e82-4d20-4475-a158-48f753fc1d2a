# 🚀 How to Run VolvoFlashWR with Real Hardware

## ✅ Problem Fixed!

The `Run_Real_Hardware_Mode.bat` script has been updated to correctly locate the executable files. You now have multiple options to run the application with real hardware support.

## 🎯 Quick Start (Recommended)

### Option 1: Build and Run (Most Reliable)
```batch
Build_And_Run_Real_Hardware.bat
```
This script will:
- ✅ Build the application in Release mode
- ✅ Copy critical libraries to the build directory
- ✅ Start the application with real hardware support

### Option 2: Updated Real Hardware Mode
```batch
Run_Real_Hardware_Mode.bat
```
This script now automatically finds the executable in:
- `VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\`
- `VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\`
- `VolvoFlashWR_RealHardware_Export\Application\`

### Option 3: Normal Mode (Auto-detection)
```batch
run_normal_mode.bat
```
This script builds the application and runs it with auto-detection of hardware.

## 🔧 Manual Steps (If Needed)

If you prefer to run manually:

1. **Build the Application**:
   ```bash
   dotnet build --configuration Release --framework net8.0-windows
   ```

2. **Navigate to Build Directory**:
   ```bash
   cd VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64
   ```

3. **Copy Critical Libraries** (Optional but recommended):
   ```bash
   copy ..\..\..\..\..\..\Libraries\WUDFPuma.dll .
   copy ..\..\..\..\..\..\Libraries\apci.dll .
   copy ..\..\..\..\..\..\Libraries\Volvo.ApciPlus.dll .
   ```

4. **Run the Application**:
   ```bash
   VolvoFlashWR.Launcher.exe --mode=normal --hardware=real
   ```

## 📋 Prerequisites Checklist

Before running, ensure:

- ✅ **138 libraries downloaded** (run `Verify_Libraries.bat` to confirm)
- ✅ **Vocom 1 adapter connected** via USB
- ✅ **Vocom driver installed** (CommunicationUnitInstaller-*******.msi)
- ✅ **Application built** (Release configuration recommended)

## 🔍 Troubleshooting

### Issue: "VolvoFlashWR.Launcher.exe not found"
**Solution**: Use `Build_And_Run_Real_Hardware.bat` which builds first, then runs.

### Issue: Application starts in dummy mode
**Solutions**:
1. Check Vocom adapter is connected and recognized by Windows
2. Verify Vocom driver is installed
3. Run `Verify_Libraries.bat` to ensure all 138 libraries are present
4. Check application logs for specific error messages

### Issue: Build errors
**Solutions**:
1. Ensure .NET 8.0 SDK is installed
2. Run `dotnet restore` to restore NuGet packages
3. Check for any missing dependencies

## 🎮 Application Modes

### Real Hardware Mode
- **Purpose**: Direct communication with Vocom adapter
- **Requirements**: Vocom adapter connected, driver installed, 138 libraries
- **Command**: `--mode=normal --hardware=real`

### Normal Mode (Auto-detection)
- **Purpose**: Automatically detects hardware, falls back to dummy if needed
- **Requirements**: Same as real hardware mode (optional)
- **Command**: `--mode=normal`

### Dummy Mode
- **Purpose**: Simulation for testing without hardware
- **Requirements**: None (built-in simulation)
- **Command**: `--mode=dummy`

## 📊 Success Indicators

When running successfully, you should see:
- ✅ Application window opens
- ✅ "Connected to Vocom adapter" in logs
- ✅ ECU communication options available
- ✅ No "falling back to dummy mode" messages

## 🔗 Related Files

- **`Verify_Libraries.bat`** - Confirms all 138 libraries are present
- **`Configure_Real_Hardware_Libraries.ps1`** - Sets up library configuration
- **`FINAL_REAL_HARDWARE_SUMMARY.md`** - Complete technical details
- **`REAL_HARDWARE_SETUP_COMPLETE.md`** - Setup documentation

## 🎉 You're Ready!

The VolvoFlashWR application is now fully configured with 138 libraries for professional-grade ECU communication. Choose your preferred method above and start communicating with real Volvo ECUs!

**Happy flashing! 🚗⚡**
