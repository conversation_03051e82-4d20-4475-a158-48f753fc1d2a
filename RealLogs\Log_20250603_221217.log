Log started at 6/3/2025 10:12:17 PM
2025-06-03 22:12:17.760 [Information] LoggingService: Logging service initialized
2025-06-03 22:12:17.777 [Information] AppConfigurationService: Initializing configuration service
2025-06-03 22:12:17.778 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Config
2025-06-03 22:12:17.779 [Information] AppConfigurationService: Configuration file not found, creating default
2025-06-03 22:12:17.783 [Warning] AppConfigurationService: Configuration service not initialized
2025-06-03 22:12:17.783 [Information] AppConfigurationService: Default configuration created
2025-06-03 22:12:17.784 [Information] AppConfigurationService: Configuration service initialized successfully
2025-06-03 22:12:17.784 [Information] App: Configuration service initialized successfully
2025-06-03 22:12:17.787 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-06-03 22:12:17.788 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: ''
2025-06-03 22:12:17.788 [Information] App: Environment variable exists: False, not 'false': True
2025-06-03 22:12:17.789 [Information] App: Final useDummyImplementations value: False
2025-06-03 22:12:17.789 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: ''
2025-06-03 22:12:17.789 [Information] App: usePatchedImplementation flag is: False
2025-06-03 22:12:17.789 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: ''
2025-06-03 22:12:17.790 [Information] App: APCI_LIBRARY_PATH environment variable is set to: ''
2025-06-03 22:12:17.790 [Information] App: VERBOSE_LOGGING environment variable is set to: ''
2025-06-03 22:12:17.790 [Information] App: verboseLogging flag is: False
2025-06-03 22:12:17.793 [Information] App: Verifying real hardware requirements...
2025-06-03 22:12:17.793 [Warning] App: ✗ Missing critical library: WUDFPuma.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\WUDFPuma.dll
2025-06-03 22:12:17.794 [Warning] App: ✗ Missing critical library: apci.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\apci.dll
2025-06-03 22:12:17.794 [Warning] App: ✗ Missing critical library: Volvo.ApciPlus.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\Volvo.ApciPlus.dll
2025-06-03 22:12:17.794 [Warning] App: ✗ Missing critical library: Volvo.ApciPlusData.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\Volvo.ApciPlusData.dll
2025-06-03 22:12:17.795 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-03 22:12:17.795 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-06-03 22:12:17.796 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Vocom\config.json
2025-06-03 22:12:17.796 [Warning] App: ⚠ Some real hardware requirements are missing - application may fall back to dummy mode
2025-06-03 22:12:17.808 [Information] App: Creating standard VocomServiceFactory instance
2025-06-03 22:12:17.809 [Information] App: Successfully created standard VocomServiceFactory instance
2025-06-03 22:12:17.809 [Information] App: Using VolvoFlashWR.Communication.Vocom.VocomServiceFactory Vocom service factory
2025-06-03 22:12:17.809 [Information] App: Checking if PTT application is running before creating Vocom service
2025-06-03 22:12:17.858 [Information] App: Creating Vocom service (attempt 1/3)
2025-06-03 22:12:17.861 [Information] VocomServiceFactory: Creating Vocom service with default settings
2025-06-03 22:12:17.862 [Information] VocomServiceFactory: Phoenix Vocom adapter not enabled, skipping
2025-06-03 22:12:17.862 [Information] VocomServiceFactory: Phoenix adapter initialization failed, attempting to create standard Vocom driver
2025-06-03 22:12:17.864 [Information] VocomDriver: Initializing Vocom driver
2025-06-03 22:12:17.866 [Information] VocomNativeInterop: Initializing Vocom driver
2025-06-03 22:12:17.873 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-06-03 22:12:17.873 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-03 22:12:17.874 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-03 22:12:17.875 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-03 22:12:17.876 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-06-03 22:12:17.885 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-06-03 22:12:17.892 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-06-03 22:12:17.894 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-06-03 22:12:17.897 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-06-03 22:12:17.897 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-06-03 22:12:17.898 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-06-03 22:12:17.904 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-06-03 22:12:17.907 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-06-03 22:12:17.910 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-06-03 22:12:17.912 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-06-03 22:12:17.913 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-06-03 22:12:17.913 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-06-03 22:12:17.913 [Information] VocomDriver: Vocom driver initialized successfully
2025-06-03 22:12:17.916 [Information] VocomService: Initializing Vocom service with dependencies
2025-06-03 22:12:17.917 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-06-03 22:12:17.919 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-06-03 22:12:17.921 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-06-03 22:12:18.013 [Information] WiFiCommunicationService: WiFi is available
2025-06-03 22:12:18.014 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-06-03 22:12:18.015 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-06-03 22:12:18.017 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-06-03 22:12:18.020 [Information] BluetoothCommunicationService: Bluetooth is available
2025-06-03 22:12:18.021 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-06-03 22:12:18.023 [Information] VocomService: Initializing Vocom service
2025-06-03 22:12:18.025 [Information] VocomService: Checking if PTT application is running
2025-06-03 22:12:18.041 [Information] VocomService: PTT application is not running
2025-06-03 22:12:18.044 [Information] VocomService: Vocom service initialized successfully
2025-06-03 22:12:18.046 [Information] VocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-06-03 22:12:18.046 [Information] App: Initializing Vocom service
2025-06-03 22:12:18.046 [Information] VocomService: Initializing Vocom service
2025-06-03 22:12:18.047 [Information] VocomService: Checking if PTT application is running
2025-06-03 22:12:18.062 [Information] VocomService: PTT application is not running
2025-06-03 22:12:18.063 [Information] VocomService: Vocom service initialized successfully
2025-06-03 22:12:18.066 [Information] VocomService: Scanning for Vocom devices
2025-06-03 22:12:18.072 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-03 22:12:18.104 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-03 22:12:18.108 [Information] VocomService: Found 2 Vocom devices
2025-06-03 22:12:18.109 [Information] App: Found 2 Vocom devices, attempting to connect to the first one
2025-06-03 22:12:18.112 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-03 22:12:18.113 [Information] VocomService: Checking if PTT application is running
2025-06-03 22:12:18.127 [Information] VocomService: PTT application is not running
2025-06-03 22:12:18.129 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-03 22:12:18.131 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-06-03 22:12:18.934 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-06-03 22:12:18.935 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-06-03 22:12:18.936 [Information] App: Connected to Vocom device Vocom - 88890300 (Bluetooth)
2025-06-03 22:12:18.941 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-06-03 22:12:18.945 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-03 22:12:18.946 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-06-03 22:12:18.949 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-03 22:12:18.951 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-03 22:12:18.952 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-03 22:12:18.956 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-03 22:12:18.958 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-03 22:12:18.973 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-03 22:12:18.976 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-03 22:12:18.979 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-03 22:12:18.987 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-03 22:12:18.991 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-03 22:12:19.002 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-03 22:12:19.003 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-03 22:12:19.004 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-03 22:12:19.005 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-03 22:12:19.005 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-03 22:12:19.005 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-03 22:12:19.006 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-03 22:12:19.006 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-03 22:12:19.006 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-03 22:12:19.011 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-03 22:12:19.011 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-03 22:12:19.011 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-03 22:12:19.012 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-03 22:12:19.012 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-03 22:12:19.012 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-03 22:12:19.012 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-03 22:12:19.013 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-03 22:12:19.016 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-03 22:12:19.019 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.020 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.020 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.021 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.022 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.024 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.027 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.028 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.030 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.031 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-03 22:12:19.033 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-03 22:12:19.034 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-03 22:12:19.038 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-03 22:12:19.041 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.041 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.042 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.042 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.042 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.042 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.043 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.043 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.043 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.044 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.044 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.046 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.054 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.054 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.054 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.055 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.055 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.055 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.056 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.056 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.056 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.056 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.057 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.057 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.063 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.063 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.063 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.064 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.064 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.064 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.064 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.065 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.065 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.065 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.066 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.066 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.072 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.072 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.072 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.073 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.073 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.073 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.073 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.074 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.074 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.074 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.075 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.075 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.081 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.081 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.081 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.082 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.082 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.082 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.082 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.083 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.083 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.083 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.084 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.084 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.090 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.090 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.090 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.091 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.091 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.091 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.092 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.092 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.092 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.093 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.093 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.093 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.099 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.099 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.099 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.100 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.100 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.100 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.101 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.101 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.101 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.102 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.102 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.102 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.108 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.108 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.108 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.109 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.109 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.109 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.109 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.110 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.110 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.110 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.111 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.111 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.117 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.117 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.117 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.118 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.118 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.118 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.118 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.119 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.119 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.120 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.120 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.120 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.127 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.127 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.127 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.128 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.128 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.128 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.129 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.129 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.129 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.130 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.130 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.130 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.136 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.136 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.137 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.137 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.137 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.138 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.138 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.138 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.139 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.139 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.139 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.139 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.146 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.146 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.146 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.146 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.147 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.147 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.147 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.148 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.148 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.148 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.149 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.149 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.162 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.162 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.163 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.163 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.163 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.163 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.164 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.164 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.164 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.165 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.165 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.165 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.172 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.172 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.172 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.173 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.173 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.173 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.173 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.174 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.174 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.174 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.175 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.175 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.181 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.181 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.181 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.182 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.182 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.182 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.182 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.183 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.183 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.183 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.184 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.184 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.191 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.191 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.191 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.191 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.192 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.192 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.192 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.193 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.193 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.193 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.193 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.194 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.200 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.200 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.200 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.201 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.201 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.201 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.202 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.202 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.202 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.203 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.203 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.203 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.210 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.210 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.210 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.210 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.211 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.211 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.211 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.212 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.212 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.212 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.212 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.213 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.218 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.218 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.218 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.219 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.219 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.220 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.220 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.221 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.221 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.221 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.222 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.222 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.227 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:19.228 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.228 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.228 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.229 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:19.229 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.229 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.230 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.230 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.230 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.231 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.231 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:19.236 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-03 22:12:19.238 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-03 22:12:19.241 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-03 22:12:19.241 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-03 22:12:19.253 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-03 22:12:19.254 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-03 22:12:19.254 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-03 22:12:19.257 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:19.258 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:19.258 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:19.258 [Information] VocomService: Using generic data transfer
2025-06-03 22:12:19.260 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-03 22:12:19.261 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-03 22:12:19.261 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.261 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:19.262 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:19.264 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-03 22:12:19.265 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:19.266 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-03 22:12:19.267 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-03 22:12:19.269 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-03 22:12:19.271 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-03 22:12:19.281 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-03 22:12:19.282 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-03 22:12:19.283 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-03 22:12:19.293 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-03 22:12:19.304 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-03 22:12:19.315 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-03 22:12:19.326 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-03 22:12:19.337 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-03 22:12:19.340 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-03 22:12:19.341 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-03 22:12:19.351 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-03 22:12:19.353 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-03 22:12:19.353 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-03 22:12:19.364 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-03 22:12:19.375 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-03 22:12:19.386 [Information] IICProtocolHandler: Enabling IIC module
2025-06-03 22:12:19.397 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-03 22:12:19.408 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-03 22:12:19.419 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-03 22:12:19.422 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-03 22:12:19.423 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-03 22:12:19.433 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-03 22:12:19.435 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-03 22:12:19.435 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-03 22:12:19.435 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-03 22:12:19.436 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-03 22:12:19.436 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-03 22:12:19.436 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-03 22:12:19.437 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-03 22:12:19.439 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-03 22:12:19.439 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-03 22:12:19.439 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-03 22:12:19.439 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-03 22:12:19.440 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-03 22:12:19.440 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-03 22:12:19.440 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-03 22:12:19.441 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-03 22:12:19.441 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-03 22:12:19.541 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-03 22:12:19.542 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-03 22:12:19.548 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-03 22:12:19.552 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-03 22:12:19.553 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-03 22:12:19.554 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-03 22:12:19.555 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-03 22:12:19.556 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-03 22:12:19.557 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-03 22:12:19.558 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-03 22:12:19.559 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-03 22:12:19.559 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-03 22:12:19.560 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-03 22:12:19.561 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-03 22:12:19.562 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-03 22:12:19.564 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-06-03 22:12:19.573 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-06-03 22:12:19.577 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-06-03 22:12:19.584 [Information] BackupService: Initializing backup service
2025-06-03 22:12:19.585 [Information] BackupService: Created backup directory: Backups
2025-06-03 22:12:19.585 [Information] BackupService: Backup service initialized successfully
2025-06-03 22:12:19.586 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-06-03 22:12:19.586 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-06-03 22:12:19.592 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-06-03 22:12:19.672 [Information] BackupService: Compressing backup data
2025-06-03 22:12:19.680 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (450 bytes)
2025-06-03 22:12:19.680 [Information] BackupServiceFactory: Created template for category: Production
2025-06-03 22:12:19.681 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-06-03 22:12:19.682 [Information] BackupService: Compressing backup data
2025-06-03 22:12:19.690 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (452 bytes)
2025-06-03 22:12:19.692 [Information] BackupServiceFactory: Created template for category: Development
2025-06-03 22:12:19.692 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-06-03 22:12:19.693 [Information] BackupService: Compressing backup data
2025-06-03 22:12:19.694 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (449 bytes)
2025-06-03 22:12:19.694 [Information] BackupServiceFactory: Created template for category: Testing
2025-06-03 22:12:19.695 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-06-03 22:12:19.695 [Information] BackupService: Compressing backup data
2025-06-03 22:12:19.697 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-06-03 22:12:19.697 [Information] BackupServiceFactory: Created template for category: Archived
2025-06-03 22:12:19.698 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-06-03 22:12:19.698 [Information] BackupService: Compressing backup data
2025-06-03 22:12:19.699 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (448 bytes)
2025-06-03 22:12:19.700 [Information] BackupServiceFactory: Created template for category: Critical
2025-06-03 22:12:19.700 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-06-03 22:12:19.701 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-06-03 22:12:19.701 [Information] BackupService: Compressing backup data
2025-06-03 22:12:19.706 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-06-03 22:12:19.707 [Information] BackupServiceFactory: Created template with predefined tags
2025-06-03 22:12:19.707 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-06-03 22:12:19.709 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-06-03 22:12:19.714 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-03 22:12:19.715 [Information] BackupSchedulerService: Created schedules directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules
2025-06-03 22:12:19.717 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-03 22:12:19.717 [Information] BackupSchedulerService: Schedules file not found: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-03 22:12:19.718 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-03 22:12:19.719 [Information] BackupSchedulerService: Starting backup scheduler
2025-06-03 22:12:19.719 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-06-03 22:12:19.720 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-06-03 22:12:19.722 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-06-03 22:12:19.723 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-06-03 22:12:19.727 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-06-03 22:12:19.728 [Information] App: Flash operation monitor service initialized successfully
2025-06-03 22:12:19.742 [Information] LicensingService: Initializing licensing service
2025-06-03 22:12:19.815 [Information] LicensingService: License information saved successfully
2025-06-03 22:12:19.818 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-06-03 22:12:19.822 [Information] App: Licensing service initialized successfully
2025-06-03 22:12:19.823 [Information] App: License status: Trial
2025-06-03 22:12:19.824 [Information] App: Trial period: 30 days remaining
2025-06-03 22:12:19.826 [Information] BackupSchedulerService: Getting all backup schedules
2025-06-03 22:12:19.828 [Information] App: Creating default backup schedules
2025-06-03 22:12:19.834 [Information] ECUCommunicationService: Scanning for ECUs
2025-06-03 22:12:19.835 [Information] ECUCommunicationService: Scanning for ECUs on the CAN bus...
2025-06-03 22:12:20.337 [Information] ECUCommunicationService: Scanning for ECUs using SPI protocol...
2025-06-03 22:12:20.637 [Information] ECUCommunicationService: Scanning for ECUs using SCI protocol...
2025-06-03 22:12:20.938 [Information] ECUCommunicationService: Scanning for ECUs using IIC protocol...
2025-06-03 22:12:21.240 [Information] ECUCommunicationService: Found 10 ECUs
2025-06-03 22:12:21.243 [Information] BackupSchedulerService: Creating backup schedule for ECU EMS
2025-06-03 22:12:21.246 [Information] BackupSchedulerService: Saving backup schedules to disk
2025-06-03 22:12:21.296 [Information] BackupSchedulerService: Saved 1 backup schedules to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-03 22:12:21.298 [Information] BackupSchedulerService: Backup schedule created for ECU EMS
2025-06-03 22:12:21.298 [Information] App: Created daily backup schedule for EMS
2025-06-03 22:12:21.299 [Information] BackupSchedulerService: Creating backup schedule for ECU EMS
2025-06-03 22:12:21.300 [Information] BackupSchedulerService: Saving backup schedules to disk
2025-06-03 22:12:21.310 [Information] BackupSchedulerService: Saved 2 backup schedules to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-03 22:12:21.311 [Information] BackupSchedulerService: Backup schedule created for ECU EMS
2025-06-03 22:12:21.311 [Information] App: Created weekly backup schedule for EMS
2025-06-03 22:12:21.496 [Information] VocomService: Initializing Vocom service
2025-06-03 22:12:21.497 [Information] VocomService: Checking if PTT application is running
2025-06-03 22:12:21.533 [Information] VocomService: PTT application is not running
2025-06-03 22:12:21.535 [Information] VocomService: Vocom service initialized successfully
2025-06-03 22:12:21.586 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-03 22:12:21.587 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-03 22:12:21.588 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-03 22:12:21.589 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-03 22:12:21.589 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-03 22:12:21.594 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-03 22:12:21.595 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-03 22:12:21.601 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-03 22:12:21.602 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-03 22:12:21.603 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-03 22:12:21.614 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-03 22:12:21.615 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-03 22:12:21.616 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-03 22:12:21.617 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-03 22:12:21.617 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-03 22:12:21.618 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-03 22:12:21.618 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-03 22:12:21.618 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-03 22:12:21.619 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-03 22:12:21.619 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-03 22:12:21.619 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-03 22:12:21.620 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-03 22:12:21.620 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-03 22:12:21.621 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-03 22:12:21.622 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-03 22:12:21.622 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-03 22:12:21.623 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-03 22:12:21.623 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-03 22:12:21.624 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.624 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.624 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.624 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.625 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.625 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.625 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.626 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.627 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.627 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-03 22:12:21.628 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-03 22:12:21.629 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-03 22:12:21.629 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-03 22:12:21.629 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.630 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.630 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.630 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.631 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.631 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.631 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.633 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.634 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.635 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.635 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.636 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.642 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.643 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.643 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.643 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.644 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.644 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.644 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.645 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.645 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.646 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.646 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.647 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.654 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.655 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.655 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.655 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.656 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.656 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.656 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.656 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.657 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.657 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.658 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.658 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.664 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.665 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.665 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.665 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.665 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.666 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.666 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.666 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.667 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.667 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.667 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.668 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.674 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.675 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.675 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.675 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.675 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.676 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.676 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.676 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.677 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.677 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.677 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.678 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.684 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.685 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.685 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.685 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.685 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.686 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.686 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.687 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.687 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.688 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.689 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.689 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.695 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.696 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.696 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.696 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.696 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.697 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.697 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.697 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.697 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.698 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.698 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.699 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.705 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.706 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.706 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.706 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.707 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.707 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.707 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.707 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.708 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.708 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.709 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.709 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.715 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.716 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.716 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.716 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.716 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.717 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.717 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.717 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.718 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.718 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.718 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.719 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.725 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.726 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.726 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.726 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.726 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.727 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.727 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.727 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.728 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.728 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.728 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.729 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.735 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.736 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.736 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.736 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.736 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.737 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.737 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.739 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.739 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.740 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.740 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.741 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.747 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.748 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.748 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.748 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.748 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.749 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.749 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.749 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.750 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.750 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.750 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.751 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.757 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.758 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.758 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.758 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.758 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.759 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.759 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.759 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.760 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.761 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.761 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.762 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.768 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.769 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.769 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.769 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.770 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.770 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.770 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.771 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.771 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.772 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.773 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.773 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.780 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.781 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.781 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.781 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.782 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.782 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.782 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.782 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.783 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.783 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.784 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.784 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.790 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.791 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.791 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.791 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.792 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.792 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.792 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.793 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.793 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.794 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.794 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.795 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.800 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.800 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.801 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.801 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.801 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.802 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.802 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.802 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.803 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.803 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.803 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.804 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.810 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.811 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.811 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.811 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.811 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.812 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.812 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.812 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.812 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.813 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.813 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.814 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.820 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.821 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.821 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.822 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.822 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.822 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.822 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.824 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.824 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.824 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.825 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.825 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.831 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-03 22:12:21.832 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.832 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.832 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.832 [Information] VocomService: Detected CAN protocol request
2025-06-03 22:12:21.833 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.833 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.833 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.833 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.834 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.834 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.835 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-03 22:12:21.841 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-03 22:12:21.842 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-03 22:12:21.842 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-03 22:12:21.842 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-03 22:12:21.853 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-03 22:12:21.854 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-03 22:12:21.854 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-03 22:12:21.855 [Information] VocomService: Sending data and waiting for response
2025-06-03 22:12:21.855 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-03 22:12:21.855 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-03 22:12:21.856 [Information] VocomService: Using generic data transfer
2025-06-03 22:12:21.856 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-03 22:12:21.856 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-03 22:12:21.856 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.858 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-03 22:12:21.859 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-03 22:12:21.859 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-03 22:12:21.860 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-03 22:12:21.860 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-03 22:12:21.861 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-03 22:12:21.861 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-03 22:12:21.862 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-03 22:12:21.872 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-03 22:12:21.873 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-03 22:12:21.873 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-03 22:12:21.884 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-03 22:12:21.895 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-03 22:12:21.906 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-03 22:12:21.917 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-03 22:12:21.928 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-03 22:12:21.940 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-03 22:12:21.941 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-03 22:12:21.952 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-03 22:12:21.952 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-03 22:12:21.953 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-03 22:12:21.964 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-03 22:12:21.975 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-03 22:12:21.986 [Information] IICProtocolHandler: Enabling IIC module
2025-06-03 22:12:21.997 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-03 22:12:22.008 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-03 22:12:22.019 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-03 22:12:22.019 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-03 22:12:22.020 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-03 22:12:22.031 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-03 22:12:22.031 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-03 22:12:22.031 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-03 22:12:22.032 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-03 22:12:22.032 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-03 22:12:22.032 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-03 22:12:22.033 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-03 22:12:22.033 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-03 22:12:22.033 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-03 22:12:22.033 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-03 22:12:22.034 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-03 22:12:22.034 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-03 22:12:22.035 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-03 22:12:22.035 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-03 22:12:22.035 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-03 22:12:22.036 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-03 22:12:22.036 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-03 22:12:22.136 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-03 22:12:22.137 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-03 22:12:22.138 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-03 22:12:22.139 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-03 22:12:22.140 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-03 22:12:22.141 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-03 22:12:22.142 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-03 22:12:22.143 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-03 22:12:22.144 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-03 22:12:22.145 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-03 22:12:22.145 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-03 22:12:22.146 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-03 22:12:22.147 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-03 22:12:22.148 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-03 22:12:22.148 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-03 22:12:22.200 [Information] BackupService: Initializing backup service
2025-06-03 22:12:22.200 [Information] BackupService: Backup service initialized successfully
2025-06-03 22:12:22.252 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-03 22:12:22.252 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-03 22:12:22.276 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-03 22:12:22.278 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-03 22:12:22.330 [Information] BackupService: Getting predefined backup categories
2025-06-03 22:12:22.382 [Information] MainViewModel: Services initialized successfully
2025-06-03 22:12:22.391 [Information] MainViewModel: Scanning for Vocom devices
2025-06-03 22:12:22.395 [Information] VocomService: Scanning for Vocom devices
2025-06-03 22:12:22.396 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-03 22:12:22.398 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-03 22:12:22.403 [Information] VocomService: Found 2 Vocom devices
2025-06-03 22:12:22.407 [Information] MainViewModel: Found 2 Vocom device(s)
