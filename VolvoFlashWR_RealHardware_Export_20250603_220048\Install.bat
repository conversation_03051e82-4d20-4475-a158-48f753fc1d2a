@echo off
setlocal enabledelayedexpansion

echo ========================================
echo VolvoFlashWR Real Hardware Installation
echo ========================================
echo.
echo This script will set up VolvoFlashWR for real hardware use.
echo.

REM Check if running as administrator
net session >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo This script requires administrator privileges.
    echo Please run as administrator.
    pause
    exit /b 1
)

echo Installing VolvoFlashWR Real Hardware Mode...
echo.

REM Create application directory
set INSTALL_DIR=C:\VolvoFlashWR
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy application files
echo Copying application files...
xcopy "Application\*" "%INSTALL_DIR%\" /E /I /Y

REM Copy libraries to system location
echo Installing libraries...
if not exist "%INSTALL_DIR%\Libraries" mkdir "%INSTALL_DIR%\Libraries"
xcopy "Libraries\*" "%INSTALL_DIR%\Libraries\" /E /I /Y

REM Create desktop shortcut
echo Creating desktop shortcut...
set SHORTCUT_PATH=%USERPROFILE%\Desktop\VolvoFlashWR Real Hardware.lnk
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT_PATH%'); $Shortcut.TargetPath = '%INSTALL_DIR%\VolvoFlashWR.Launcher.exe'; $Shortcut.Arguments = '--mode=normal --hardware=real'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\VolvoFlashWR.ico'; $Shortcut.Description = 'VolvoFlashWR Real Hardware Mode'; $Shortcut.Save()"

echo Installation completed successfully
echo.
echo You can now run VolvoFlashWR from:
echo - Desktop shortcut: VolvoFlashWR Real Hardware
echo - Start menu: Search for "VolvoFlashWR"
echo - Direct path: %INSTALL_DIR%\VolvoFlashWR.Launcher.exe
echo.
pause
