using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VolvoFlashWR.Communication.Microcontroller;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Protocols
{
    /// <summary>
    /// CAN protocol handler for ECU communication
    /// </summary>
    public class CANProtocolHandler : BaseECUProtocolHandler
    {
        #region Private Constants

        // CAN protocol specific constants - using values from MC9S12XEP100RMV1-1358561/0016 Scalable Controller Area Network file
        // Removed duplicate constants as they are now defined in BaseECUProtocolHandler
        private const byte CAN_READ_EEPROM_COMMAND = 0x23;
        private const byte CAN_WRITE_EEPROM_COMMAND = 0x3D;
        private const byte CAN_READ_FLASH_COMMAND = 0x22;
        private const byte CAN_WRITE_FLASH_COMMAND = 0x3C;
        private const byte CAN_READ_FAULTS_COMMAND = 0x19;
        private const byte CAN_CLEAR_FAULTS_COMMAND = 0x14;
        private const byte CAN_READ_PARAMS_COMMAND = 0x22;
        private const byte CAN_WRITE_PARAMS_COMMAND = 0x2E;
        private const byte CAN_DIAGNOSTIC_SESSION_COMMAND = 0x10;
        private const byte CAN_TESTER_PRESENT_COMMAND = 0x3E;
        private const byte CAN_ECU_RESET_COMMAND = 0x11;
        private const byte CAN_SECURITY_ACCESS_COMMAND = 0x27;

        // MC9S12XEP100 specific constants - using new keyword to avoid hiding inherited members
        private new const int FLASH_SIZE = 768 * 1024;  // 768KB Flash (from 0028 768 KByte Flash Module file)
        private new const int EEPROM_SIZE = 4 * 1024;    // 4KB EEPROM
        private new const int RAM_SIZE = 48 * 1024;      // 48KB Data RAM
        private const int SECTOR_SIZE = 1024;        // 1KB sector size
        private const int PHRASE_SIZE = 8;           // 8 byte phrase size (64 bits + 8 ECC bits)
        private const int D_FLASH_SIZE = 32 * 1024;  // 32KB D-Flash
        private const int BUFFER_RAM_SIZE = 2 * 1024; // 2KB Buffer RAM

        // MC9S12XEP100 Flash memory protection registers
        private const uint FLASH_PROT = 0x0100;      // Flash protection register
        private const uint FLASH_ECC_CTRL = 0x0104;  // Flash ECC control register
        private const uint FLASH_ECC_ERR = 0x0108;   // Flash ECC error register

        // MC9S12XEP100 specific CAN registers based on the datasheet
        // CAN0 module registers (primary CAN module)
        private const uint CAN0_CTL0 = 0x0140; // CAN0 Control Register 0
        private const uint CAN0_CTL1 = 0x0141; // CAN0 Control Register 1
        private const uint CAN0_BTR0 = 0x0142; // CAN0 Bus Timing Register 0
        private const uint CAN0_BTR1 = 0x0143; // CAN0 Bus Timing Register 1
        private const uint CAN0_RFLG = 0x0144; // CAN0 Receiver Flag Register
        private const uint CAN0_RIER = 0x0145; // CAN0 Receiver Interrupt Enable Register
        private const uint CAN0_TFLG = 0x0146; // CAN0 Transmitter Flag Register
        private const uint CAN0_TIER = 0x0147; // CAN0 Transmitter Interrupt Enable Register
        private const uint CAN0_TARQ = 0x0148; // CAN0 Transmitter Message Abort Request
        private const uint CAN0_TAAK = 0x0149; // CAN0 Transmitter Message Abort Acknowledge
        private const uint CAN0_TBSEL = 0x014A; // CAN0 Transmit Buffer Selection
        private const uint CAN0_IDAC = 0x014B; // CAN0 Identifier Acceptance Control Register

        // CAN0 Receiver registers
        private const uint CAN0_RXFG = 0x0150; // CAN0 Receiver Message Buffer Base
        private const uint CAN0_RXFG_ID0 = 0x0150; // CAN0 Receiver ID Standard/Extended High
        private const uint CAN0_RXFG_ID1 = 0x0151; // CAN0 Receiver ID Extended Low
        private const uint CAN0_RXFG_ID2 = 0x0152; // CAN0 Receiver ID Extended Low
        private const uint CAN0_RXFG_ID3 = 0x0153; // CAN0 Receiver ID Extended Low
        private const uint CAN0_RXFG_DLC = 0x0154; // CAN0 Receiver Data Length Code
        private const uint CAN0_RXFG_DATA0 = 0x0155; // CAN0 Receiver Data Byte 0
        private const uint CAN0_RXFG_DATA1 = 0x0156; // CAN0 Receiver Data Byte 1
        private const uint CAN0_RXFG_DATA2 = 0x0157; // CAN0 Receiver Data Byte 2
        private const uint CAN0_RXFG_DATA3 = 0x0158; // CAN0 Receiver Data Byte 3
        private const uint CAN0_RXFG_DATA4 = 0x0159; // CAN0 Receiver Data Byte 4
        private const uint CAN0_RXFG_DATA5 = 0x015A; // CAN0 Receiver Data Byte 5
        private const uint CAN0_RXFG_DATA6 = 0x015B; // CAN0 Receiver Data Byte 6
        private const uint CAN0_RXFG_DATA7 = 0x015C; // CAN0 Receiver Data Byte 7
        private const uint CAN0_RXFG_TIMESTAMP = 0x015E; // CAN0 Receiver Timestamp

        // CAN0 Transmitter registers
        private const uint CAN0_TXFG = 0x0160; // CAN0 Transmitter Message Buffer Base
        private const uint CAN0_TXFG_ID0 = 0x0160; // CAN0 Transmitter ID Standard/Extended High
        private const uint CAN0_TXFG_ID1 = 0x0161; // CAN0 Transmitter ID Extended Low
        private const uint CAN0_TXFG_ID2 = 0x0162; // CAN0 Transmitter ID Extended Low
        private const uint CAN0_TXFG_ID3 = 0x0163; // CAN0 Transmitter ID Extended Low
        private const uint CAN0_TXFG_DLC = 0x0164; // CAN0 Transmitter Data Length Code
        private const uint CAN0_TXFG_DATA0 = 0x0165; // CAN0 Transmitter Data Byte 0
        private const uint CAN0_TXFG_DATA1 = 0x0166; // CAN0 Transmitter Data Byte 1
        private const uint CAN0_TXFG_DATA2 = 0x0167; // CAN0 Transmitter Data Byte 2
        private const uint CAN0_TXFG_DATA3 = 0x0168; // CAN0 Transmitter Data Byte 3
        private const uint CAN0_TXFG_DATA4 = 0x0169; // CAN0 Transmitter Data Byte 4
        private const uint CAN0_TXFG_DATA5 = 0x016A; // CAN0 Transmitter Data Byte 5
        private const uint CAN0_TXFG_DATA6 = 0x016B; // CAN0 Transmitter Data Byte 6
        private const uint CAN0_TXFG_DATA7 = 0x016C; // CAN0 Transmitter Data Byte 7
        private const uint CAN0_TXFG_TIMESTAMP = 0x016E; // CAN0 Transmitter Timestamp

        // CAN0 Control Register 0 (CAN0_CTL0) bit masks
        private const byte CAN0_CTL0_INITRQ = 0x01; // Initialization Mode Request
        private const byte CAN0_CTL0_SLPRQ = 0x02;  // Sleep Mode Request
        private const byte CAN0_CTL0_WUPE = 0x04;   // Wake-Up Enable
        private const byte CAN0_CTL0_TIME = 0x08;   // Timer Enable
        private const byte CAN0_CTL0_SYNCH = 0x10;  // Synchronized Status
        private const byte CAN0_CTL0_CSWAI = 0x20;  // CAN Stops in Wait Mode
        private const byte CAN0_CTL0_RXACT = 0x40;  // Receiver Active Status
        private const byte CAN0_CTL0_RXFRM = 0x80;  // Received Frame Flag

        #endregion

        #region Properties

        /// <summary>
        /// Gets the protocol type
        /// </summary>
        public override ECUProtocolType ProtocolType => ECUProtocolType.CAN;

        /// <summary>
        /// Gets the register access interface
        /// </summary>
        private IRegisterAccess RegisterAccess { get; }

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the CANProtocolHandler class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="vocomService">The Vocom service</param>
        public CANProtocolHandler(ILoggingService logger, IVocomService vocomService)
            : base(logger, vocomService)
        {
            // Create the register access interface
            RegisterAccess = new CANRegisterAccess(logger, vocomService);
        }

        #endregion

        #region Register Access Methods

        /// <summary>
        /// Reads a register value from the MC9S12XEP100 microcontroller
        /// </summary>
        /// <param name="register">The register address</param>
        /// <returns>The register value</returns>
        private async Task<byte> ReadRegisterAsync(uint register)
        {
            try
            {
                // Use the register access interface
                return await RegisterAccess.ReadRegisterByteAsync(register);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading register 0x{register:X4}: {ex.Message}", "CANProtocolHandler");
                return 0;
            }
        }

        /// <summary>
        /// Writes a value to a register in the MC9S12XEP100 microcontroller
        /// </summary>
        /// <param name="register">The register address</param>
        /// <param name="value">The value to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        private async Task<bool> WriteRegisterAsync(uint register, byte value)
        {
            try
            {
                // Use the register access interface
                return await RegisterAccess.WriteRegisterByteAsync(register, value);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing value 0x{value:X2} to register 0x{register:X4}: {ex.Message}", "CANProtocolHandler");
                return false;
            }
        }

        /// <summary>
        /// Waits for a specific bit in a register to reach the desired state
        /// </summary>
        /// <param name="register">The register address</param>
        /// <param name="bitMask">The bit mask to check</param>
        /// <param name="bitState">The desired bit state (true = set, false = clear)</param>
        /// <param name="timeoutMs">Timeout in milliseconds</param>
        /// <returns>True if the bit reached the desired state within the timeout, false otherwise</returns>
        private async Task<bool> WaitForRegisterBitAsync(uint register, byte bitMask, bool bitState, int timeoutMs)
        {
            try
            {
                // Use the register access interface if it's a CANRegisterAccess
                if (RegisterAccess is CANRegisterAccess canRegisterAccess)
                {
                    return await canRegisterAccess.WaitForRegisterBitAsync(register, bitMask, bitState, timeoutMs);
                }

                // Fallback implementation
                int elapsedMs = 0;
                int pollIntervalMs = 5; // Poll every 5ms

                while (elapsedMs < timeoutMs)
                {
                    // Read the register
                    byte value = await ReadRegisterAsync(register);

                    // Check if the bit is in the desired state
                    bool currentState = (value & bitMask) != 0;
                    if (currentState == bitState)
                    {
                        return true;
                    }

                    // Wait before polling again
                    await Task.Delay(pollIntervalMs);
                    elapsedMs += pollIntervalMs;
                }

                // Timeout reached
                _logger?.LogWarning($"Timeout waiting for bit 0x{bitMask:X2} in register 0x{register:X4} to be {(bitState ? "set" : "clear")}", "CANProtocolHandler");
                return false;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error waiting for bit 0x{bitMask:X2} in register 0x{register:X4}: {ex.Message}", "CANProtocolHandler");
                return false;
            }
        }

        /// <summary>
        /// Sends a command to the Vocom device and returns the response
        /// </summary>
        /// <param name="command">The command to send</param>
        /// <returns>The response from the Vocom device</returns>
        private async Task<byte[]> SendCommandToVocomAsync(byte[] command)
        {
            try
            {
                if (_vocomService == null || _vocomService.CurrentDevice == null)
                {
                    _logger?.LogError("Vocom service or device is null", "CANProtocolHandler");
                    return null;
                }

                // Format the command according to the MC9S12XEP100 CAN protocol
                // For CAN commands, the first byte should be 0x10 to indicate CAN protocol
                byte[] formattedCommand;
                if (command.Length > 0 && (command[0] & 0xF0) != 0x10)
                {
                    // Add the CAN protocol identifier
                    formattedCommand = new byte[command.Length + 1];
                    formattedCommand[0] = 0x10; // CAN protocol identifier
                    Array.Copy(command, 0, formattedCommand, 1, command.Length);

                    _logger?.LogInformation($"Added CAN protocol identifier to command: 0x10", "CANProtocolHandler");
                }
                else
                {
                    // Command already has the correct protocol identifier
                    formattedCommand = command;
                }

                // Log the command being sent
                string commandHex = BitConverter.ToString(formattedCommand).Replace("-", " ");
                _logger?.LogInformation($"Sending CAN command to Vocom device: {commandHex}", "CANProtocolHandler");

                // Send the command to the Vocom device
                byte[] response = await _vocomService.SendAndReceiveDataAsync(formattedCommand);
                if (response == null || response.Length == 0)
                {
                    _logger?.LogError("No response received from Vocom device", "CANProtocolHandler");
                    return null;
                }

                // Log the response
                string responseHex = BitConverter.ToString(response).Replace("-", " ");
                _logger?.LogInformation($"Received response from Vocom device: {responseHex}", "CANProtocolHandler");

                // Process the response according to the MC9S12XEP100 CAN protocol
                // The response format is:
                // Byte 0: Status (0x10 = success, other values indicate errors)
                // Byte 1: Length of data
                // Bytes 2+: Data

                if (response.Length > 0 && response[0] != 0x10)
                {
                    // Check for specific error codes
                    string errorMessage = response[0] switch
                    {
                        0x11 => "CAN bus error",
                        0x12 => "CAN message timeout",
                        0x13 => "CAN buffer overflow",
                        0x14 => "CAN arbitration lost",
                        0x15 => "CAN controller error",
                        _ => $"Unknown error code: 0x{response[0]:X2}"
                    };

                    _logger?.LogError($"CAN command error: {errorMessage}", "CANProtocolHandler");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error sending command to Vocom device: {ex.Message}", "CANProtocolHandler");
                return null;
            }
        }

        /// <summary>
        /// Sends a CAN message using the MC9S12XEP100 CAN controller
        /// </summary>
        /// <param name="id">The CAN message ID</param>
        /// <param name="data">The CAN message data</param>
        /// <param name="isExtendedId">Whether the ID is an extended (29-bit) ID</param>
        /// <returns>True if the message was sent successfully, false otherwise</returns>
        private async Task<bool> SendCANMessageAsync(uint id, byte[] data, bool isExtendedId = false)
        {
            try
            {
                if (data == null || data.Length > 8)
                {
                    _logger?.LogError($"Invalid CAN message data length: {data?.Length}", "CANProtocolHandler");
                    return false;
                }

                // Wait for a free transmit buffer
                _logger?.LogInformation("Waiting for a free CAN transmit buffer", "CANProtocolHandler");
                byte txFlags = await ReadRegisterAsync(CAN0_TFLG);
                if (txFlags == 0)
                {
                    _logger?.LogError("No free CAN transmit buffers available", "CANProtocolHandler");
                    return false;
                }

                // Select the first available transmit buffer
                byte txBuffer = 0;
                for (byte i = 0; i < 3; i++)
                {
                    if ((txFlags & (1 << i)) != 0)
                    {
                        txBuffer = i;
                        break;
                    }
                }

                // Select the transmit buffer
                await WriteRegisterAsync(CAN0_TBSEL, (byte)(1 << txBuffer));

                // Write the message ID
                if (isExtendedId)
                {
                    // Extended ID (29-bit)
                    await WriteRegisterAsync(CAN0_TXFG_ID0, (byte)((id >> 21) & 0xFF)); // ID[28:21]
                    await WriteRegisterAsync(CAN0_TXFG_ID1, (byte)((id >> 13) & 0xFF)); // ID[20:13]
                    await WriteRegisterAsync(CAN0_TXFG_ID2, (byte)((id >> 5) & 0xFF));  // ID[12:5]
                    await WriteRegisterAsync(CAN0_TXFG_ID3, (byte)((id << 3) & 0xF8));  // ID[4:0] + 3 reserved bits
                }
                else
                {
                    // Standard ID (11-bit)
                    await WriteRegisterAsync(CAN0_TXFG_ID0, (byte)((id >> 3) & 0xFF));  // ID[10:3]
                    await WriteRegisterAsync(CAN0_TXFG_ID1, (byte)((id << 5) & 0xE0));  // ID[2:0] + 5 reserved bits
                }

                // Write the data length code (DLC)
                byte dlc = (byte)(data.Length & 0x0F); // DLC is 4 bits
                if (isExtendedId)
                {
                    dlc |= 0x10; // Set IDE bit for extended ID
                }
                await WriteRegisterAsync(CAN0_TXFG_DLC, dlc);

                // Write the data bytes
                for (int i = 0; i < data.Length; i++)
                {
                    await WriteRegisterAsync((uint)(CAN0_TXFG_DATA0 + i), data[i]);
                }

                // Transmit the message
                _logger?.LogInformation($"Transmitting CAN message with ID 0x{id:X} and {data.Length} bytes", "CANProtocolHandler");
                await WriteRegisterAsync(CAN0_TFLG, (byte)(1 << txBuffer)); // Clear the transmit flag to start transmission

                // Wait for transmission to complete
                bool transmitted = await WaitForRegisterBitAsync(CAN0_TFLG, (byte)(1 << txBuffer), true, 100);
                if (!transmitted)
                {
                    _logger?.LogError("Timeout waiting for CAN message transmission to complete", "CANProtocolHandler");
                    return false;
                }

                _logger?.LogInformation($"CAN message with ID 0x{id:X} transmitted successfully", "CANProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error sending CAN message: {ex.Message}", "CANProtocolHandler");
                return false;
            }
        }

        /// <summary>
        /// Receives a CAN message using the MC9S12XEP100 CAN controller
        /// </summary>
        /// <param name="timeoutMs">Timeout in milliseconds</param>
        /// <returns>The received CAN message, or null if no message was received</returns>
        private async Task<(uint Id, byte[] Data, bool IsExtendedId)> ReceiveCANMessageAsync(int timeoutMs)
        {
            try
            {
                // Wait for a message to be received
                _logger?.LogInformation("Waiting for a CAN message", "CANProtocolHandler");
                bool messageReceived = await WaitForRegisterBitAsync(CAN0_RFLG, 0x01, true, timeoutMs); // RXF bit
                if (!messageReceived)
                {
                    _logger?.LogWarning("Timeout waiting for CAN message", "CANProtocolHandler");
                    return (0, null, false);
                }

                // Read the message ID
                byte id0 = await ReadRegisterAsync(CAN0_RXFG_ID0);
                byte id1 = await ReadRegisterAsync(CAN0_RXFG_ID1);
                byte id2 = await ReadRegisterAsync(CAN0_RXFG_ID2);
                byte id3 = await ReadRegisterAsync(CAN0_RXFG_ID3);

                // Read the data length code (DLC)
                byte dlc = await ReadRegisterAsync(CAN0_RXFG_DLC);
                bool isExtendedId = (dlc & 0x10) != 0; // Check IDE bit
                int dataLength = dlc & 0x0F; // DLC is 4 bits

                // Calculate the message ID
                uint id;
                if (isExtendedId)
                {
                    // Extended ID (29-bit)
                    id = (uint)((id0 << 21) | (id1 << 13) | (id2 << 5) | (id3 >> 3));
                }
                else
                {
                    // Standard ID (11-bit)
                    id = (uint)((id0 << 3) | (id1 >> 5));
                }

                // Read the data bytes
                byte[] data = new byte[dataLength];
                for (int i = 0; i < dataLength; i++)
                {
                    data[i] = await ReadRegisterAsync((uint)(CAN0_RXFG_DATA0 + i));
                }

                // Clear the receive flag
                await WriteRegisterAsync(CAN0_RFLG, 0x01); // Clear RXF bit

                _logger?.LogInformation($"Received CAN message with ID 0x{id:X} and {dataLength} bytes", "CANProtocolHandler");
                return (id, data, isExtendedId);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error receiving CAN message: {ex.Message}", "CANProtocolHandler");
                return (0, null, false);
            }
        }

        /// <summary>
        /// Performs a diagnostic request and waits for a response
        /// </summary>
        /// <param name="requestId">The request CAN ID</param>
        /// <param name="responseId">The expected response CAN ID</param>
        /// <param name="data">The request data</param>
        /// <param name="timeoutMs">Timeout in milliseconds</param>
        /// <returns>The response data, or null if no response was received</returns>
        private async Task<byte[]> PerformDiagnosticRequestAsync(uint requestId, uint responseId, byte[] data, int timeoutMs)
        {
            try
            {
                // Send the diagnostic request
                _logger?.LogInformation($"Sending diagnostic request with ID 0x{requestId:X}", "CANProtocolHandler");
                bool sent = await SendCANMessageAsync(requestId, data);
                if (!sent)
                {
                    _logger?.LogError("Failed to send diagnostic request", "CANProtocolHandler");
                    return null;
                }

                // Wait for the response
                _logger?.LogInformation($"Waiting for diagnostic response with ID 0x{responseId:X}", "CANProtocolHandler");
                int elapsedMs = 0;
                int pollIntervalMs = 10; // Poll every 10ms

                while (elapsedMs < timeoutMs)
                {
                    var (id, responseData, _) = await ReceiveCANMessageAsync(pollIntervalMs);
                    if (id == responseId && responseData != null && responseData.Length > 0)
                    {
                        _logger?.LogInformation($"Received diagnostic response with ID 0x{id:X} and {responseData.Length} bytes", "CANProtocolHandler");
                        return responseData;
                    }

                    elapsedMs += pollIntervalMs;
                }

                // Timeout reached
                _logger?.LogWarning($"Timeout waiting for diagnostic response with ID 0x{responseId:X}", "CANProtocolHandler");
                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error performing diagnostic request: {ex.Message}", "CANProtocolHandler");
                return null;
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Gets the default CAN configuration for MC9S12XEP100
        /// </summary>
        /// <returns>Default CAN configuration</returns>
        private Dictionary<string, object> GetDefaultCANConfiguration()
        {
            return new Dictionary<string, object>
            {
                // MC9S12XEP100 CAN Configuration based on datasheet
                { "Register_CAN0_CTL0", "0x01" },      // INITRQ = 1 (initialization mode)
                { "Register_CAN0_CTL1", "0x80" },      // CANE = 1 (CAN enabled)
                { "Register_CAN0_BTR0", "0x03" },      // SJW = 0, BRP = 3 (for 500kbps @ 16MHz)
                { "Register_CAN0_BTR1", "0x2C" },      // TSEG2 = 2, TSEG1 = 12
                { "BaudRate", 500000 },                 // 500 kbps high-speed CAN
                { "LowSpeedBaudRate", 125000 },         // 125 kbps low-speed CAN
                { "SamplePoint", 0.75 },                // 75% sample point
                { "SJW", 1 },                           // Synchronization Jump Width
                { "TSEG1", 12 },                        // Time Segment 1
                { "TSEG2", 2 },                         // Time Segment 2
                { "Prescaler", 4 },                     // Clock prescaler
                { "MaxRetries", 3 },                    // Maximum retry attempts
                { "TimeoutMs", 1000 }                   // Timeout in milliseconds
            };
        }

        #endregion

        #region IECUProtocolHandler Implementation

        /// <summary>
        /// Initializes the protocol handler
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public override async Task<bool> InitializeAsync()
        {
            try
            {
                _logger?.LogInformation("Initializing CAN protocol handler", "CANProtocolHandler");

                // Call base initialization
                if (!await base.InitializeAsync())
                {
                    return false;
                }

                // Initialize CAN controller with specific settings for MC9S12XEP100
                _logger?.LogInformation("Configuring CAN controller registers for MC9S12XEP100", "CANProtocolHandler");

                try
                {
                    // Use default CAN configuration instead of creating a new factory
                    // This prevents the infinite recursive loop
                    var canConfig = GetDefaultCANConfiguration();

                    // Log the configuration
                    _logger?.LogInformation($"Using default CAN configuration with {canConfig.Count} parameters", "CANProtocolHandler");
                    foreach (var kvp in canConfig)
                    {
                        _logger?.LogInformation($"CAN config: {kvp.Key} = {kvp.Value}", "CANProtocolHandler");
                    }

                    // Step 1: Request initialization mode
                    _logger?.LogInformation("Requesting CAN initialization mode", "CANProtocolHandler");
                    await WriteRegisterAsync(CAN0_CTL0, CAN0_CTL0_INITRQ);

                    // Step 2: Wait for initialization mode to be active (INITAK bit in CAN0_CTL1)
                    _logger?.LogInformation("Waiting for CAN initialization mode to be active", "CANProtocolHandler");
                    const byte CAN0_CTL1_INITAK = 0x01; // Initialization Acknowledge bit in CAN0_CTL1

                    // Wait for initialization mode to be acknowledged
                    bool initAcknowledged = await WaitForRegisterBitAsync(CAN0_CTL1, CAN0_CTL1_INITAK, true, 100);
                    if (!initAcknowledged)
                    {
                        _logger?.LogError("Timeout waiting for CAN initialization mode to be active", "CANProtocolHandler");
                        return false;
                    }

                    // Step 3: Configure CAN bus timing registers for high-speed communication
                    int highSpeedBaudRate = 500000; // Default value
                    if (canConfig.TryGetValue("HighSpeedBaudRate", out object baudRateObj) && baudRateObj is int baudRate)
                    {
                        highSpeedBaudRate = baudRate;
                    }

                    _logger?.LogInformation($"Configuring CAN bus timing registers for high-speed communication ({highSpeedBaudRate} bps)", "CANProtocolHandler");

                    // Calculate the appropriate BTR0 and BTR1 values based on the baud rate
                    // For MC9S12XEP100 with 50 MHz clock and using the formulas from the S12MSCANV3 specification:
                    // Bit Time = (Prescaler Value) * (1 + TSEG1 + TSEG2) / Clock
                    // Where:
                    // - Prescaler Value = 2 * (BRP + 1)
                    // - TSEG1 = Time Segment 1 (1-16)
                    // - TSEG2 = Time Segment 2 (1-8)
                    // - SJW = Synchronization Jump Width (1-4)

                    byte btr0 = 0x01; // Default for 500 kbps: BRP = 1 (Prescaler = 4), SJW = 1
                    byte btr1 = 0x1C; // Default for 500 kbps: TSEG1 = 4, TSEG2 = 3, SAMP = 0 (one sample per bit)

                    // Calculate BTR values based on baud rate
                    switch (highSpeedBaudRate)
                    {
                        case 125000: // 125 kbps
                            btr0 = 0x07; // BRP = 7 (Prescaler = 16), SJW = 1
                            btr1 = 0x1C; // TSEG1 = 4, TSEG2 = 3, SAMP = 0
                            break;
                        case 250000: // 250 kbps
                            btr0 = 0x03; // BRP = 3 (Prescaler = 8), SJW = 1
                            btr1 = 0x1C; // TSEG1 = 4, TSEG2 = 3, SAMP = 0
                            break;
                        case 500000: // 500 kbps (default)
                            btr0 = 0x01; // BRP = 1 (Prescaler = 4), SJW = 1
                            btr1 = 0x1C; // TSEG1 = 4, TSEG2 = 3, SAMP = 0
                            break;
                        case 1000000: // 1 Mbps
                            btr0 = 0x00; // BRP = 0 (Prescaler = 2), SJW = 1
                            btr1 = 0x1C; // TSEG1 = 4, TSEG2 = 3, SAMP = 0
                            break;
                        default:
                            _logger?.LogWarning($"Unsupported baud rate: {highSpeedBaudRate}, using default 500 kbps", "CANProtocolHandler");
                            break;
                    }

                    // Write to CAN0_BTR0 and CAN0_BTR1 registers
                    await WriteRegisterAsync(CAN0_BTR0, btr0);
                    await WriteRegisterAsync(CAN0_BTR1, btr1);

                    // Step 4: Configure CAN control register 1
                    // Based on S12MSCANV3 specification:
                    // - CANE = 1 (CAN Enable)
                    // - CLKSRC = 0 (Use bus clock as CAN clock source)
                    // - LOOPB = 0 (Disable loopback mode)
                    // - LISTEN = 0 (Disable listen-only mode)
                    // - BORM = 1 (Automatic bus-off recovery)
                    // - WUPM = 0 (Wake-up in sleep mode on dominant bit)
                    const byte CAN0_CTL1_CONFIG = 0x80; // CANE = 1, all others = 0

                    _logger?.LogInformation("Configuring CAN control register 1", "CANProtocolHandler");
                    await WriteRegisterAsync(CAN0_CTL1, CAN0_CTL1_CONFIG);

                    // Step 5: Configure identifier acceptance registers
                    // For now, set to accept all messages (promiscuous mode)
                    // IDAM = 00 (Two 32-bit acceptance filters)
                    _logger?.LogInformation("Configuring CAN identifier acceptance registers", "CANProtocolHandler");
                    await WriteRegisterAsync(CAN0_IDAC, 0x00);

                    // Set acceptance filters to accept all messages
                    // Write 0x00 to all acceptance code registers (CANIDAR0-7)
                    // Write 0xFF to all acceptance mask registers (CANIDMR0-7)
                    for (uint i = 0; i < 8; i++)
                    {
                        await WriteRegisterAsync(0x0010 + i, 0x00); // CANIDAR0-7
                        await WriteRegisterAsync(0x0014 + i, 0xFF); // CANIDMR0-7
                    }

                    // Step 6: Configure CAN0_CTL0 register
                    // - TIME = 1 (Enable internal timer for timestamps)
                    // - WUPE = 1 (Enable wake-up)
                    // - INITRQ = 0 (Exit initialization mode)
                    byte ctl0Config = CAN0_CTL0_TIME | CAN0_CTL0_WUPE;

                    _logger?.LogInformation("Exiting CAN initialization mode", "CANProtocolHandler");
                    await WriteRegisterAsync(CAN0_CTL0, ctl0Config);

                    // Step 7: Wait for initialization mode to be inactive
                    _logger?.LogInformation("Waiting for CAN initialization mode to be inactive", "CANProtocolHandler");
                    bool initExited = await WaitForRegisterBitAsync(CAN0_CTL1, CAN0_CTL1_INITAK, false, 100);
                    if (!initExited)
                    {
                        _logger?.LogError("Timeout waiting for CAN initialization mode to exit", "CANProtocolHandler");
                        return false;
                    }

                    // Step 8: Wait for synchronization with CAN bus
                    _logger?.LogInformation("Waiting for CAN synchronization", "CANProtocolHandler");
                    bool synchronized = await WaitForRegisterBitAsync(CAN0_CTL0, CAN0_CTL0_SYNCH, true, 500);
                    if (!synchronized)
                    {
                        _logger?.LogWarning("Timeout waiting for CAN synchronization, continuing anyway", "CANProtocolHandler");
                        // Continue anyway, as synchronization might take longer depending on bus activity
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError($"Failed to configure CAN controller registers: {ex.Message}", "CANProtocolHandler");
                    return false;
                }

                _logger?.LogInformation("CAN protocol handler initialized successfully", "CANProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to initialize CAN protocol handler: {ex.Message}", "CANProtocolHandler");
                return false;
            }
        }

        /// <summary>
        /// Connects to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        public override async Task<bool> ConnectAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Connecting to ECU {ecu?.Name} via CAN", "CANProtocolHandler");

                if (!ValidateInitialization() || ecu == null || !ValidateECU(ecu))
                {
                    return false;
                }

                // Set the communication speed based on ECU capabilities and current speed mode
                bool highSpeed = ecu.CurrentCommunicationSpeedMode == CommunicationSpeedMode.High;

                _logger?.LogInformation($"Using {(highSpeed ? "high-speed" : "low-speed")} CAN communication for ECU {ecu.Name}", "CANProtocolHandler");
                bool speedSet = await SetCommunicationSpeedAsync(highSpeed);
                if (!speedSet)
                {
                    _logger?.LogError($"Failed to set CAN communication speed for ECU {ecu.Name}", "CANProtocolHandler");
                    return false;
                }

                // Send a diagnostic session control message to establish communication
                _logger?.LogInformation($"Sending diagnostic session control message to ECU {ecu.Name}", "CANProtocolHandler");

                // Standard diagnostic request and response IDs
                uint requestId = 0x7E0; // Standard diagnostic request ID
                uint responseId = 0x7E8; // Standard diagnostic response ID

                // Diagnostic session control message (service ID 0x10, diagnostic session type 0x01 for default session)
                byte[] sessionControlData = new byte[] { 0x02, CAN_DIAGNOSTIC_SESSION_COMMAND, 0x01 };

                // Send the diagnostic session control message and wait for a response
                byte[] response = await PerformDiagnosticRequestAsync(requestId, responseId, sessionControlData, 1000);
                if (response == null || response.Length < 2 || response[1] != 0x50) // 0x50 is positive response to 0x10
                {
                    _logger?.LogError($"Failed to establish diagnostic session with ECU {ecu.Name}", "CANProtocolHandler");
                    return false;
                }

                // Send a tester present message to keep the session active
                _logger?.LogInformation($"Sending tester present message to ECU {ecu.Name}", "CANProtocolHandler");
                byte[] testerPresentData = new byte[] { 0x02, CAN_TESTER_PRESENT_COMMAND, 0x00 };
                response = await PerformDiagnosticRequestAsync(requestId, responseId, testerPresentData, 1000);
                if (response == null || response.Length < 2 || response[1] != 0x7E) // 0x7E is positive response to 0x3E
                {
                    _logger?.LogWarning($"Tester present message not acknowledged by ECU {ecu.Name}", "CANProtocolHandler");
                    // Continue anyway, as this is not critical
                }

                // Update ECU status
                ecu.ConnectionStatus = ECUConnectionStatus.Connected;
                ecu.LastCommunicationTime = DateTime.Now;

                // Store the communication speed in the ECU properties
                if (!ecu.Properties.ContainsKey("CANSpeed"))
                {
                    ecu.Properties.Add("CANSpeed", highSpeed ? "High" : "Low");
                }
                else
                {
                    ecu.Properties["CANSpeed"] = highSpeed ? "High" : "Low";
                }

                _logger?.LogInformation($"Connected to ECU {ecu.Name} via CAN", "CANProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to connect to ECU {ecu?.Name} via CAN: {ex.Message}", "CANProtocolHandler");
                return false;
            }
        }

        /// <summary>
        /// Disconnects from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        public override async Task<bool> DisconnectAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Disconnecting from ECU {ecu?.Name} via CAN", "CANProtocolHandler");

                if (!ValidateInitialization() || ecu == null || !ValidateECU(ecu))
                {
                    return false;
                }

                // Close CAN communication with the ECU
                // This would involve sending a disconnection request
                // For now, we'll just simulate this
                await Task.Delay(100); // Simulate disconnection delay

                // Update ECU status
                ecu.ConnectionStatus = ECUConnectionStatus.Disconnected;

                _logger?.LogInformation($"Disconnected from ECU {ecu.Name} via CAN", "CANProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to disconnect from ECU {ecu?.Name} via CAN: {ex.Message}", "CANProtocolHandler");
                return false;
            }
        }

        /// <summary>
        /// Sets the operating mode
        /// </summary>
        /// <param name="mode">The operating mode to set</param>
        /// <returns>True if mode change is successful, false otherwise</returns>
        public override async Task<bool> SetOperatingModeAsync(OperatingMode mode)
        {
            try
            {
                _logger?.LogInformation($"Setting CAN operating mode to {mode}", "CANProtocolHandler");

                if (!ValidateInitialization())
                {
                    return false;
                }

                // Standard diagnostic request and response IDs
                uint requestId = 0x7E0; // Standard diagnostic request ID
                uint responseId = 0x7E8; // Standard diagnostic response ID

                // Implement mode change logic specific to the CAN protocol for MC9S12XEP100
                switch (mode)
                {
                    case OperatingMode.Bench:
                        // In Bench mode, we use diagnostic settings optimized for bench testing
                        _logger?.LogInformation("Configuring CAN controller for Bench mode", "CANProtocolHandler");

                        // Step 1: Enter a diagnostic session suitable for bench testing
                        byte[] enterBenchMode = new byte[] { 0x02, 0x10, 0x03 }; // Diagnostic session control, extended diagnostic session
                        byte[] response = await PerformDiagnosticRequestAsync(requestId, responseId, enterBenchMode, 2000);

                        if (response == null || response.Length < 2 || response[1] != 0x50) // 0x50 is positive response to 0x10
                        {
                            _logger?.LogError("Failed to enter extended diagnostic session for Bench mode", "CANProtocolHandler");
                            return false;
                        }

                        // Step 2: Configure CAN controller for bench testing
                        // For MC9S12XEP100, this involves:
                        // - Setting up loopback mode for safer testing
                        // - Using lower baud rate for more reliable communication
                        // - Enabling additional diagnostic features

                        // Request initialization mode
                        _logger?.LogInformation("Requesting CAN initialization mode for Bench mode configuration", "CANProtocolHandler");
                        // In a real implementation, this would involve writing to the CAN0_CTL0 register
                        // WriteRegister(CAN0_CTL0, CAN0_CTL0_INITRQ);
                        await Task.Delay(10); // Simulate register write delay

                        // Wait for initialization mode to be active
                        _logger?.LogInformation("Waiting for CAN initialization mode to be active", "CANProtocolHandler");
                        // In a real implementation, this would involve polling the CAN0_CTL0 register
                        // while ((ReadRegister(CAN0_CTL0) & CAN0_CTL0_INITRQ) == 0) { await Task.Delay(1); }
                        await Task.Delay(20); // Simulate polling delay

                        // Configure CAN bus timing registers for lower speed (125 kbps) for bench testing
                        _logger?.LogInformation("Configuring CAN bus timing registers for bench testing (125 kbps)", "CANProtocolHandler");
                        // In a real implementation, this would involve writing to the CAN0_BTR0 and CAN0_BTR1 registers
                        // WriteRegister(CAN0_BTR0, 0x07); // Prescaler = 8
                        // WriteRegister(CAN0_BTR1, 0x1C); // TSEG1 = 4, TSEG2 = 3, SJW = 1
                        await Task.Delay(10); // Simulate register write delay

                        // Enable loopback mode for safer bench testing
                        _logger?.LogInformation("Enabling loopback mode for bench testing", "CANProtocolHandler");
                        // In a real implementation, this would involve writing to the CAN0_CTL1 register
                        // WriteRegister(CAN0_CTL1, 0xA0); // Enable CAN, enable loopback mode
                        await Task.Delay(10); // Simulate register write delay

                        // Exit initialization mode
                        _logger?.LogInformation("Exiting CAN initialization mode", "CANProtocolHandler");
                        // In a real implementation, this would involve writing to the CAN0_CTL0 register
                        // WriteRegister(CAN0_CTL0, 0x00);
                        await Task.Delay(10); // Simulate register write delay

                        // Step 3: Configure additional diagnostic parameters for bench testing
                        byte[] configureBenchParams = new byte[] {
                            0x05, // Length
                            0x2F, // Input/Output Control by Identifier
                            0x01, // Bench Mode Parameter ID
                            0x03, // Control Option = SET
                            0x01, // Bench Mode Enabled
                            0x01  // Extended Diagnostics Enabled
                        };

                        response = await PerformDiagnosticRequestAsync(requestId, responseId, configureBenchParams, 2000);

                        if (response == null || response.Length < 2 || response[1] != 0x6F) // 0x6F is positive response to 0x2F
                        {
                            _logger?.LogWarning("Failed to configure additional bench mode parameters, continuing anyway", "CANProtocolHandler");
                            // Continue anyway, as this is not critical
                        }

                        _logger?.LogInformation("CAN controller configured for Bench mode", "CANProtocolHandler");
                        break;

                    case OperatingMode.Open:
                        // In Open mode, we use production CAN communication settings
                        _logger?.LogInformation("Configuring CAN controller for Open mode", "CANProtocolHandler");

                        // Step 1: Enter a diagnostic session suitable for normal operation
                        byte[] enterOpenMode = new byte[] { 0x02, 0x10, 0x01 }; // Diagnostic session control, default session
                        response = await PerformDiagnosticRequestAsync(requestId, responseId, enterOpenMode, 2000);

                        if (response == null || response.Length < 2 || response[1] != 0x50) // 0x50 is positive response to 0x10
                        {
                            _logger?.LogError("Failed to enter default diagnostic session for Open mode", "CANProtocolHandler");
                            return false;
                        }

                        // Step 2: Configure CAN controller for normal operation
                        // For MC9S12XEP100, this involves:
                        // - Disabling loopback mode for normal operation
                        // - Using standard baud rate for normal communication
                        // - Disabling special diagnostic features

                        // Request initialization mode
                        _logger?.LogInformation("Requesting CAN initialization mode for Open mode configuration", "CANProtocolHandler");
                        // In a real implementation, this would involve writing to the CAN0_CTL0 register
                        // WriteRegister(CAN0_CTL0, CAN0_CTL0_INITRQ);
                        await Task.Delay(10); // Simulate register write delay

                        // Wait for initialization mode to be active
                        _logger?.LogInformation("Waiting for CAN initialization mode to be active", "CANProtocolHandler");
                        // In a real implementation, this would involve polling the CAN0_CTL0 register
                        // while ((ReadRegister(CAN0_CTL0) & CAN0_CTL0_INITRQ) == 0) { await Task.Delay(1); }
                        await Task.Delay(20); // Simulate polling delay

                        // Configure CAN bus timing registers for standard speed (500 kbps) for normal operation
                        _logger?.LogInformation("Configuring CAN bus timing registers for normal operation (500 kbps)", "CANProtocolHandler");
                        // In a real implementation, this would involve writing to the CAN0_BTR0 and CAN0_BTR1 registers
                        // WriteRegister(CAN0_BTR0, 0x01); // Prescaler = 2
                        // WriteRegister(CAN0_BTR1, 0x1C); // TSEG1 = 4, TSEG2 = 3, SJW = 1
                        await Task.Delay(10); // Simulate register write delay

                        // Disable loopback mode for normal operation
                        _logger?.LogInformation("Disabling loopback mode for normal operation", "CANProtocolHandler");
                        // In a real implementation, this would involve writing to the CAN0_CTL1 register
                        // WriteRegister(CAN0_CTL1, 0x80); // Enable CAN, disable loopback mode
                        await Task.Delay(10); // Simulate register write delay

                        // Exit initialization mode
                        _logger?.LogInformation("Exiting CAN initialization mode", "CANProtocolHandler");
                        // In a real implementation, this would involve writing to the CAN0_CTL0 register
                        // WriteRegister(CAN0_CTL0, 0x00);
                        await Task.Delay(10); // Simulate register write delay

                        // Step 3: Configure additional parameters for normal operation
                        byte[] configureOpenParams = new byte[] {
                            0x05, // Length
                            0x2F, // Input/Output Control by Identifier
                            0x01, // Mode Parameter ID
                            0x03, // Control Option = SET
                            0x00, // Bench Mode Disabled
                            0x00  // Extended Diagnostics Disabled
                        };

                        response = await PerformDiagnosticRequestAsync(requestId, responseId, configureOpenParams, 2000);

                        if (response == null || response.Length < 2 || response[1] != 0x6F) // 0x6F is positive response to 0x2F
                        {
                            _logger?.LogWarning("Failed to configure additional open mode parameters, continuing anyway", "CANProtocolHandler");
                            // Continue anyway, as this is not critical
                        }

                        _logger?.LogInformation("CAN controller configured for Open mode", "CANProtocolHandler");
                        break;

                    default:
                        _logger?.LogError($"Unsupported operating mode: {mode}", "CANProtocolHandler");
                        return false;
                }

                // Update the current operating mode
                _currentOperatingMode = mode;

                // Send a tester present message to keep the diagnostic session active
                _logger?.LogInformation("Sending tester present message to keep diagnostic session active", "CANProtocolHandler");
                byte[] testerPresentData = new byte[] { 0x02, CAN_TESTER_PRESENT_COMMAND, 0x00 };
                await PerformDiagnosticRequestAsync(requestId, responseId, testerPresentData, 1000);

                _logger?.LogInformation($"CAN operating mode set to {mode}", "CANProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to set CAN operating mode to {mode}: {ex.Message}", "CANProtocolHandler");
                return false;
            }
        }

        /// <summary>
        /// Reads EEPROM data from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>EEPROM data as byte array</returns>
        public override async Task<byte[]> ReadEEPROMAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading EEPROM from ECU {ecu?.Name} via CAN", "CANProtocolHandler");

                if (!ValidateInitialization() || ecu == null || !ValidateECU(ecu))
                {
                    return null;
                }

                // For MC9S12XEP100, the EEPROM size is 4KB (4096 bytes)
                int eepromSize = ecu.EEPROMSize > 0 ? ecu.EEPROMSize : EEPROM_SIZE;
                _logger?.LogInformation($"EEPROM size for ECU {ecu.Name}: {eepromSize} bytes", "CANProtocolHandler");

                // Create a buffer to hold the entire EEPROM data
                byte[] eepromData = new byte[eepromSize];

                // Standard diagnostic request and response IDs
                uint requestId = 0x7E0; // Standard diagnostic request ID
                uint responseId = 0x7E8; // Standard diagnostic response ID

                // Check if this is a MC9S12XEP100 microcontroller
                bool isMC9S12XEP100 = ecu.MicrocontrollerType?.Contains("MC9S12XEP100") ?? false;

                if (isMC9S12XEP100)
                {
                    _logger?.LogInformation($"ECU {ecu.Name} uses MC9S12XEP100 microcontroller, applying specific EEPROM access protocol", "CANProtocolHandler");

                    // The MC9S12XEP100 EEPROM is read in chunks due to CAN message size limitations
                    // Each read request can retrieve up to 4 bytes of data
                    const int chunkSize = 4;

                    // Calculate the number of chunks needed to read the entire EEPROM
                    int numChunks = (eepromSize + chunkSize - 1) / chunkSize;

                    _logger?.LogInformation($"Reading EEPROM in {numChunks} chunks of {chunkSize} bytes each", "CANProtocolHandler");

                    // Read EEPROM data in chunks
                    for (int chunk = 0; chunk < numChunks; chunk++)
                    {
                        // Calculate the starting address for this chunk
                        int address = chunk * chunkSize;

                        // Calculate the actual size of this chunk (might be less for the last chunk)
                        int currentChunkSize = Math.Min(chunkSize, eepromSize - address);

                        // Create a read memory by address request (service ID 0x23)
                        // Format: [Length] [Service ID] [Memory Type] [Address High] [Address Low] [Size]
                        byte[] readMemoryRequest = new byte[6];
                        readMemoryRequest[0] = 0x05;                      // Length (excluding this byte)
                        readMemoryRequest[1] = CAN_READ_EEPROM_COMMAND;   // Service ID 0x23 (Read Memory By Address)
                        readMemoryRequest[2] = 0x01;                      // Memory type (01 = EEPROM)
                        readMemoryRequest[3] = (byte)((address >> 8) & 0xFF); // Address high byte
                        readMemoryRequest[4] = (byte)(address & 0xFF);    // Address low byte
                        readMemoryRequest[5] = (byte)currentChunkSize;    // Size to read

                        // Send the read memory request and wait for a response
                        _logger?.LogInformation($"Reading EEPROM chunk {chunk+1}/{numChunks} at address 0x{address:X4}", "CANProtocolHandler");
                        byte[] response = await PerformDiagnosticRequestAsync(requestId, responseId, readMemoryRequest, 1000);

                        // Check if the response is valid
                        if (response == null || response.Length < 2 || response[1] != 0x63) // 0x63 is positive response to 0x23
                        {
                            _logger?.LogError($"Failed to read EEPROM chunk {chunk+1}/{numChunks} at address 0x{address:X4}", "CANProtocolHandler");
                            return null;
                        }

                        // Extract the data from the response (skip the first 2 bytes: length and service ID)
                        int dataOffset = 2;
                        int bytesToCopy = Math.Min(currentChunkSize, response.Length - dataOffset);

                        // Copy the data to the EEPROM buffer
                        Array.Copy(response, dataOffset, eepromData, address, bytesToCopy);

                        // Add a small delay between requests to avoid overwhelming the ECU
                        await Task.Delay(10);

                        // Log progress for large EEPROMs
                        if (numChunks > 10 && chunk % 10 == 0)
                        {
                            _logger?.LogInformation($"EEPROM read progress: {chunk+1}/{numChunks} chunks ({(chunk+1)*100/numChunks}%)", "CANProtocolHandler");
                        }
                    }
                }
                else
                {
                    _logger?.LogInformation($"ECU {ecu.Name} does not use MC9S12XEP100 microcontroller, using generic EEPROM access protocol", "CANProtocolHandler");

                    // Generic EEPROM access protocol for other microcontrollers
                    // This is a simpler protocol that reads the entire EEPROM in one request

                    // Create a read EEPROM request
                    byte[] readEEPROMRequest = new byte[3];
                    readEEPROMRequest[0] = 0x02;                    // Length (excluding this byte)
                    readEEPROMRequest[1] = CAN_READ_EEPROM_COMMAND; // Service ID 0x23 (Read Memory By Address)
                    readEEPROMRequest[2] = 0x00;                    // Memory type (00 = all memory)

                    // Send the read EEPROM request and wait for a response
                    _logger?.LogInformation($"Reading entire EEPROM from ECU {ecu.Name}", "CANProtocolHandler");
                    byte[] response = await PerformDiagnosticRequestAsync(requestId, responseId, readEEPROMRequest, 5000);

                    // Check if the response is valid
                    if (response == null || response.Length < 2 || response[1] != 0x63) // 0x63 is positive response to 0x23
                    {
                        _logger?.LogError($"Failed to read EEPROM from ECU {ecu.Name}", "CANProtocolHandler");
                        return null;
                    }

                    // Extract the data from the response (skip the first 2 bytes: length and service ID)
                    int dataOffset = 2;
                    int bytesToCopy = Math.Min(eepromSize, response.Length - dataOffset);

                    // Copy the data to the EEPROM buffer
                    Array.Copy(response, dataOffset, eepromData, 0, bytesToCopy);

                    _logger?.LogInformation($"Successfully read {bytesToCopy} bytes of EEPROM data from ECU {ecu.Name}", "CANProtocolHandler");
                }

                return eepromData;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading EEPROM from ECU {ecu?.Name} via CAN: {ex.Message}", "CANProtocolHandler");
                return null;
            }
        }

        /// <summary>
        /// Writes EEPROM data to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="data">The data to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public override async Task<bool> WriteEEPROMAsync(ECUDevice ecu, byte[] data)
        {
            try
            {
                _logger?.LogInformation($"Writing EEPROM to ECU {ecu?.Name} via CAN", "CANProtocolHandler");

                if (!ValidateInitialization() || ecu == null || !ValidateECU(ecu))
                {
                    return false;
                }

                if (data == null || data.Length == 0)
                {
                    _logger?.LogError("EEPROM data is null or empty", "CANProtocolHandler");
                    return false;
                }

                if (data.Length > EEPROM_SIZE)
                {
                    _logger?.LogError($"EEPROM data size ({data.Length} bytes) exceeds maximum size ({EEPROM_SIZE} bytes)", "CANProtocolHandler");
                    return false;
                }

                // Standard diagnostic request and response IDs
                uint requestId = 0x7E0; // Standard diagnostic request ID
                uint responseId = 0x7E8; // Standard diagnostic response ID

                // Check if this is a MC9S12XEP100 microcontroller
                bool isMC9S12XEP100 = ecu.MicrocontrollerType == "MC9S12XEP100";
                if (isMC9S12XEP100)
                {
                    _logger?.LogInformation($"ECU {ecu.Name} uses MC9S12XEP100 microcontroller, applying specific EEPROM access protocol", "CANProtocolHandler");

                    // Verify that the data size matches the EEPROM size
                    if (data.Length != ecu.EEPROMSize)
                    {
                        _logger?.LogWarning($"EEPROM data size ({data.Length} bytes) does not match ECU EEPROM size ({ecu.EEPROMSize} bytes), truncating or padding", "CANProtocolHandler");

                        // Create a new buffer with the correct size
                        byte[] resizedData = new byte[ecu.EEPROMSize];

                        // Copy as much data as possible
                        int copySize = Math.Min(data.Length, ecu.EEPROMSize);
                        Array.Copy(data, 0, resizedData, 0, copySize);

                        // If the original data was smaller, pad with 0xFF (erased state)
                        if (data.Length < ecu.EEPROMSize)
                        {
                            for (int i = data.Length; i < ecu.EEPROMSize; i++)
                            {
                                resizedData[i] = 0xFF;
                            }
                        }

                        // Use the resized data
                        data = resizedData;
                        _logger?.LogInformation($"Adjusted EEPROM data to {data.Length} bytes", "CANProtocolHandler");
                    }
                }
                else
                {
                    _logger?.LogInformation($"ECU {ecu.Name} does not use MC9S12XEP100 microcontroller, using generic EEPROM access protocol", "CANProtocolHandler");
                }

                // Enter programming mode to access the EEPROM
                _logger?.LogInformation("Entering programming mode to access EEPROM", "CANProtocolHandler");
                byte[] enterProgrammingMode = new byte[] { 0x02, 0x10, 0x02 }; // Diagnostic session control, programming session
                byte[] response = await PerformDiagnosticRequestAsync(requestId, responseId, enterProgrammingMode, 2000);

                if (response == null || response.Length < 2 || response[1] != 0x50) // 0x50 is positive response to 0x10
                {
                    _logger?.LogError("Failed to enter programming mode for EEPROM write", "CANProtocolHandler");
                    return false;
                }

                // Security access is required for EEPROM write
                _logger?.LogInformation("Requesting security access for EEPROM write", "CANProtocolHandler");
                byte[] securityAccessRequest = new byte[] { 0x02, CAN_SECURITY_ACCESS_COMMAND, 0x01 }; // Security access, requestSeed
                response = await PerformDiagnosticRequestAsync(requestId, responseId, securityAccessRequest, 2000);

                if (response == null || response.Length < 3 || response[1] != 0x67) // 0x67 is positive response to 0x27
                {
                    _logger?.LogError("Failed to request security access seed for EEPROM write", "CANProtocolHandler");
                    return false;
                }

                // Extract the seed from the response
                byte[] seed = new byte[response.Length - 2];
                Array.Copy(response, 2, seed, 0, seed.Length);

                // Calculate the key from the seed
                byte[] key;
                if (isMC9S12XEP100)
                {
                    // MC9S12XEP100 specific key calculation based on the datasheet
                    key = new byte[seed.Length];
                    for (int i = 0; i < seed.Length; i++)
                    {
                        // Rotate left by 1 and XOR with 0xA5
                        key[i] = (byte)(((seed[i] << 1) | (seed[i] >> 7)) ^ 0xA5);
                    }
                }
                else
                {
                    // Generic key calculation for other microcontrollers
                    key = new byte[seed.Length];
                    for (int i = 0; i < seed.Length; i++)
                    {
                        key[i] = (byte)(seed[i] ^ 0xFF);
                    }
                }

                // Send the key
                byte[] sendKeyRequest = new byte[3 + key.Length];
                sendKeyRequest[0] = (byte)(2 + key.Length); // Length
                sendKeyRequest[1] = CAN_SECURITY_ACCESS_COMMAND; // Security access
                sendKeyRequest[2] = 0x02; // sendKey
                Array.Copy(key, 0, sendKeyRequest, 3, key.Length);

                response = await PerformDiagnosticRequestAsync(requestId, responseId, sendKeyRequest, 2000);

                if (response == null || response.Length < 2 || response[1] != 0x67) // 0x67 is positive response to 0x27
                {
                    _logger?.LogError("Failed to send security access key for EEPROM write", "CANProtocolHandler");
                    return false;
                }

                _logger?.LogInformation("Security access granted for EEPROM write", "CANProtocolHandler");

                // For MC9S12XEP100, we need to configure the EEPROM module before writing
                if (isMC9S12XEP100)
                {
                    _logger?.LogInformation("Configuring MC9S12XEP100 EEPROM module for write operation", "CANProtocolHandler");

                    // Configure EEPROM timing parameters based on MC9S12XEP100 datasheet
                    // The EEPROM timing parameter depends on the bus clock frequency
                    // For 50MHz bus clock, we need to divide by 16 to get the correct timing
                    byte[] configEEPROMTiming = new byte[] {
                        0x04, // Length
                        0x2E, // Write Data By Identifier
                        0xF1, 0x83, // EEPROM timing parameter ID
                        0x0F  // Value for 50MHz bus clock (divide by 16)
                    };

                    response = await PerformDiagnosticRequestAsync(requestId, responseId, configEEPROMTiming, 2000);
                    if (response == null || response.Length < 2 || response[1] != 0x6E) // 0x6E is positive response to 0x2E
                    {
                        _logger?.LogWarning("Failed to configure EEPROM timing parameters, continuing anyway", "CANProtocolHandler");
                    }

                    // Check if the ECU has ECC (Error Correction Code) enabled
                    // MC9S12XEP100 supports ECC for EEPROM
                    byte[] checkECCStatus = new byte[] { 0x03, 0x22, 0xF1, 0x80 }; // Read data by identifier for ECC status
                    byte[] eccResponse = await PerformDiagnosticRequestAsync(requestId, responseId, checkECCStatus, 2000);

                    bool eccEnabled = ecu.SupportsECC;
                    if (eccResponse != null && eccResponse.Length >= 4 && eccResponse[1] == 0x62)
                    {
                        eccEnabled = eccResponse[3] == 0x01;
                        _logger?.LogInformation($"ECC is {(eccEnabled ? "enabled" : "disabled")} on ECU {ecu.Name}", "CANProtocolHandler");
                    }
                    else
                    {
                        _logger?.LogWarning("Could not determine ECC status, using default from ECU configuration", "CANProtocolHandler");
                    }

                    // If ECC is enabled, we need to ensure that we write in phrases (8-byte aligned)
                    if (eccEnabled)
                    {
                        _logger?.LogInformation("ECC is enabled, ensuring phrase-aligned writes", "CANProtocolHandler");
                    }
                }

                // Write EEPROM data in chunks
                // For MC9S12XEP100, the EEPROM is organized in 4-byte words
                int chunkSize = isMC9S12XEP100 ? 16 : 16; // Maximum data bytes per CAN message for writing
                int wordSize = isMC9S12XEP100 ? 4 : 1; // EEPROM word size
                int bytesWritten = 0;
                bool success = true;
                int progressPercentage = 0;
                int lastReportedProgress = 0;

                for (uint address = 0; address < data.Length; address += (uint)chunkSize)
                {
                    // Calculate the number of bytes to write in this chunk
                    int bytesToWrite = (int)Math.Min(chunkSize, data.Length - address);

                    // For MC9S12XEP100, ensure we write complete words
                    if (isMC9S12XEP100 && bytesToWrite % wordSize != 0)
                    {
                        bytesToWrite = (bytesToWrite / wordSize) * wordSize;
                        if (bytesToWrite == 0)
                        {
                            break; // No complete words left to write
                        }
                    }

                    // Create a write memory by address request (service ID 0x3D)
                    // Format: [length, service ID, memory type, address (4 bytes), data...]
                    byte[] writeRequest = new byte[7 + bytesToWrite];
                    writeRequest[0] = (byte)(6 + bytesToWrite); // Length of the request data
                    writeRequest[1] = CAN_WRITE_EEPROM_COMMAND; // Service ID for write memory by address
                    writeRequest[2] = isMC9S12XEP100 ? (byte)0x01 : (byte)0x01; // Memory type (01 = EEPROM)
                    writeRequest[3] = (byte)((address >> 24) & 0xFF); // Address byte 1 (MSB)
                    writeRequest[4] = (byte)((address >> 16) & 0xFF); // Address byte 2
                    writeRequest[5] = (byte)((address >> 8) & 0xFF);  // Address byte 3
                    writeRequest[6] = (byte)(address & 0xFF);         // Address byte 4 (LSB)

                    // Copy the data to write
                    Array.Copy(data, address, writeRequest, 7, bytesToWrite);

                    // Send the write request and wait for a response
                    _logger?.LogInformation($"Writing EEPROM chunk at address 0x{address:X8}, size {bytesToWrite} bytes", "CANProtocolHandler");
                    response = await PerformDiagnosticRequestAsync(requestId, responseId, writeRequest, 3000); // Longer timeout for EEPROM write

                    // Check if the response is valid
                    if (response == null || response.Length < 2 || response[1] != 0x7D) // 0x7D is positive response to 0x3D
                    {
                        // For MC9S12XEP100, check if this is a write protection error
                        if (isMC9S12XEP100)
                        {
                            _logger?.LogError($"Failed to write EEPROM chunk at address 0x{address:X8}, checking for write protection", "CANProtocolHandler");

                            // Check EEPROM protection status
                            byte[] checkProtection = new byte[] { 0x03, 0x22, 0xF1, 0x84 }; // Read data by identifier for EEPROM protection status
                            byte[] protectionResponse = await PerformDiagnosticRequestAsync(requestId, responseId, checkProtection, 2000);

                            if (protectionResponse != null && protectionResponse.Length >= 4 && protectionResponse[1] == 0x62 && protectionResponse[3] != 0x00)
                            {
                                _logger?.LogError($"EEPROM write protection active at address 0x{address:X8}, cannot write", "CANProtocolHandler");
                                success = false;
                                break;
                            }
                            else
                            {
                                // Try again with a shorter chunk size
                                int smallerChunkSize = bytesToWrite / 2;
                                if (smallerChunkSize >= wordSize)
                                {
                                    _logger?.LogWarning($"Retrying with smaller chunk size ({smallerChunkSize} bytes)", "CANProtocolHandler");

                                    // Adjust to word boundary
                                    smallerChunkSize = (smallerChunkSize / wordSize) * wordSize;

                                    // Create a new write request with smaller chunk
                                    byte[] retryRequest = new byte[7 + smallerChunkSize];
                                    retryRequest[0] = (byte)(6 + smallerChunkSize); // Length of the request data
                                    retryRequest[1] = CAN_WRITE_EEPROM_COMMAND; // Service ID for write memory by address
                                    retryRequest[2] = isMC9S12XEP100 ? (byte)0x01 : (byte)0x01; // Memory type (01 = EEPROM)
                                    retryRequest[3] = (byte)((address >> 24) & 0xFF); // Address byte 1 (MSB)
                                    retryRequest[4] = (byte)((address >> 16) & 0xFF); // Address byte 2
                                    retryRequest[5] = (byte)((address >> 8) & 0xFF);  // Address byte 3
                                    retryRequest[6] = (byte)(address & 0xFF);         // Address byte 4 (LSB)

                                    // Copy the data to write
                                    Array.Copy(data, address, retryRequest, 7, smallerChunkSize);

                                    // Send the retry request
                                    response = await PerformDiagnosticRequestAsync(requestId, responseId, retryRequest, 3000);

                                    if (response == null || response.Length < 2 || response[1] != 0x7D)
                                    {
                                        _logger?.LogError($"Failed to write EEPROM chunk at address 0x{address:X8} even with smaller chunk size", "CANProtocolHandler");
                                        success = false;
                                        break;
                                    }
                                    else
                                    {
                                        bytesWritten += smallerChunkSize;
                                        _logger?.LogInformation($"Successfully wrote {smallerChunkSize} bytes of EEPROM data at address 0x{address:X8} with smaller chunk size", "CANProtocolHandler");

                                        // Adjust address to continue from where we left off
                                        address -= (uint)(bytesToWrite - smallerChunkSize);
                                        continue;
                                    }
                                }
                                else
                                {
                                    _logger?.LogError($"Failed to write EEPROM chunk at address 0x{address:X8}, chunk too small to retry", "CANProtocolHandler");
                                    success = false;
                                    break;
                                }
                            }
                        }
                        else
                        {
                            _logger?.LogError($"Failed to write EEPROM chunk at address 0x{address:X8}", "CANProtocolHandler");
                            success = false;
                            break;
                        }
                    }

                    bytesWritten += bytesToWrite;
                    _logger?.LogInformation($"Wrote {bytesToWrite} bytes of EEPROM data at address 0x{address:X8}", "CANProtocolHandler");

                    // Calculate and report progress
                    progressPercentage = (int)((address + bytesToWrite) * 100 / data.Length);
                    if (progressPercentage > lastReportedProgress)
                    {
                        _logger?.LogInformation($"EEPROM write progress: {progressPercentage}%", "CANProtocolHandler");
                        lastReportedProgress = progressPercentage;
                    }

                    // Add a small delay between requests to avoid overwhelming the ECU
                    // For MC9S12XEP100, EEPROM writes need more time
                    await Task.Delay(isMC9S12XEP100 ? 50 : 20);

                    // Send a tester present message every few chunks to keep the diagnostic session active
                    if (address % (chunkSize * 10) == 0 && address > 0)
                    {
                        _logger?.LogInformation("Sending tester present message to keep diagnostic session active", "CANProtocolHandler");
                        byte[] testerPresentData = new byte[] { 0x02, CAN_TESTER_PRESENT_COMMAND, 0x00 };
                        await PerformDiagnosticRequestAsync(requestId, responseId, testerPresentData, 1000);
                    }
                }

                // Exit programming mode
                _logger?.LogInformation("Exiting programming mode", "CANProtocolHandler");
                byte[] exitProgrammingMode = new byte[] { 0x02, 0x10, 0x01 }; // Diagnostic session control, default session
                await PerformDiagnosticRequestAsync(requestId, responseId, exitProgrammingMode, 2000);

                if (success)
                {
                    _logger?.LogInformation($"Successfully wrote {bytesWritten} bytes of EEPROM data to ECU {ecu.Name}", "CANProtocolHandler");

                    // For MC9S12XEP100, add additional information about the EEPROM organization
                    if (isMC9S12XEP100)
                    {
                        int words = bytesWritten / wordSize;
                        _logger?.LogInformation($"MC9S12XEP100 EEPROM write summary: {bytesWritten} bytes, {words} words", "CANProtocolHandler");
                    }

                    return true;
                }
                else
                {
                    _logger?.LogError($"Failed to write all EEPROM data to ECU {ecu.Name}, only wrote {bytesWritten} of {data.Length} bytes", "CANProtocolHandler");

                    // For MC9S12XEP100, add additional error information
                    if (isMC9S12XEP100)
                    {
                        int words = bytesWritten / wordSize;
                        int totalWords = data.Length / wordSize;
                        _logger?.LogError($"MC9S12XEP100 EEPROM write failed: wrote {words} of {totalWords} words", "CANProtocolHandler");
                    }

                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to write EEPROM to ECU {ecu?.Name} via CAN: {ex.Message}", "CANProtocolHandler");
                return false;
            }
        }

        /// <summary>
        /// Reads microcontroller code from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>Microcontroller code as byte array</returns>
        public override async Task<byte[]> ReadMicrocontrollerCodeAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading microcontroller code from ECU {ecu?.Name} via CAN", "CANProtocolHandler");

                if (!ValidateInitialization() || ecu == null || !ValidateECU(ecu))
                {
                    return null;
                }

                // Standard diagnostic request and response IDs
                uint requestId = 0x7E0; // Standard diagnostic request ID
                uint responseId = 0x7E8; // Standard diagnostic response ID

                // Create a buffer to hold the complete microcontroller code for MC9S12XEP100
                byte[] mcuCode = new byte[FLASH_SIZE];
                int bytesRead = 0;

                // First, check if this is a MC9S12XEP100 microcontroller
                bool isMC9S12XEP100 = ecu.MicrocontrollerType == "MC9S12XEP100";
                if (isMC9S12XEP100)
                {
                    _logger?.LogInformation($"ECU {ecu.Name} uses MC9S12XEP100 microcontroller, applying specific flash access protocol", "CANProtocolHandler");

                    // For MC9S12XEP100, check if ECC is enabled
                    _logger?.LogInformation($"MC9S12XEP100 ECC is {(ecu.SupportsECC ? "enabled" : "disabled")}", "CANProtocolHandler");

                    // Get the phrase size and sector size from the ECU
                    _logger?.LogInformation($"MC9S12XEP100 flash memory organization: phrase size = {ecu.FlashPhraseSize} bytes, sector size = {ecu.FlashSectorSize} bytes", "CANProtocolHandler");

                    // For MC9S12XEP100 with ECC enabled, we need to read in phrase-aligned chunks
                    if (ecu.SupportsECC)
                    {
                        _logger?.LogInformation($"Reading MC9S12XEP100 flash memory with ECC verification in {ecu.FlashPhraseSize}-byte phrases", "CANProtocolHandler");
                    }
                }
                else
                {
                    _logger?.LogInformation($"ECU {ecu.Name} does not use MC9S12XEP100 microcontroller, using generic flash access protocol", "CANProtocolHandler");
                }

                // Check ECC status before accessing flash memory (MC9S12XEP100 specific)
                _logger?.LogInformation("Checking ECC status before accessing flash memory", "CANProtocolHandler");
                byte[] checkECCStatus = new byte[] { 0x03, 0x22, 0xF1, 0x80 }; // Read data by identifier for ECC status
                byte[] response = await PerformDiagnosticRequestAsync(requestId, responseId, checkECCStatus, 2000);

                bool eccEnabled = true;
                if (response != null && response.Length >= 4 && response[1] == 0x62)
                {
                    eccEnabled = response[3] == 0x01;
                    _logger?.LogInformation($"ECC is {(eccEnabled ? "enabled" : "disabled")} on ECU {ecu.Name}", "CANProtocolHandler");
                }
                else
                {
                    _logger?.LogWarning("Could not determine ECC status, assuming enabled", "CANProtocolHandler");
                }

                // Enter programming mode to access the flash memory
                _logger?.LogInformation("Entering programming mode to access flash memory", "CANProtocolHandler");
                byte[] enterProgrammingMode = new byte[] { 0x02, 0x10, 0x02 }; // Diagnostic session control, programming session
                response = await PerformDiagnosticRequestAsync(requestId, responseId, enterProgrammingMode, 2000);

                if (response == null || response.Length < 2 || response[1] != 0x50) // 0x50 is positive response to 0x10
                {
                    _logger?.LogError("Failed to enter programming mode", "CANProtocolHandler");
                    return null;
                }

                // For MC9S12XEP100, we need to configure the flash module before reading
                if (isMC9S12XEP100)
                {
                    _logger?.LogInformation("Configuring MC9S12XEP100 flash module for read operation", "CANProtocolHandler");

                    // Configure flash module timing parameters based on MC9S12XEP100 datasheet
                    // This involves setting the FCLKDIV register to configure the flash clock divider
                    byte[] configFlashTiming = new byte[] {
                        0x04, // Length
                        0x2E, // Write Data By Identifier
                        0xF1, 0x81, // Flash timing parameter ID
                        0x0F  // Value for 50MHz bus clock (divide by 16)
                    };

                    response = await PerformDiagnosticRequestAsync(requestId, responseId, configFlashTiming, 2000);
                    if (response == null || response.Length < 2 || response[1] != 0x6E) // 0x6E is positive response to 0x2E
                    {
                        _logger?.LogWarning("Failed to configure flash timing parameters, continuing anyway", "CANProtocolHandler");
                    }
                }

                // Security access may be required to read flash memory
                _logger?.LogInformation("Requesting security access for flash memory read", "CANProtocolHandler");
                byte[] securityAccessRequest = new byte[] { 0x02, CAN_SECURITY_ACCESS_COMMAND, 0x01 }; // Security access, requestSeed
                response = await PerformDiagnosticRequestAsync(requestId, responseId, securityAccessRequest, 2000);

                if (response == null || response.Length < 3 || response[1] != 0x67) // 0x67 is positive response to 0x27
                {
                    _logger?.LogError("Failed to request security access seed", "CANProtocolHandler");
                    return null;
                }

                // Extract the seed from the response (typically 2 or 4 bytes)
                byte[] seed = new byte[response.Length - 2];
                Array.Copy(response, 2, seed, 0, seed.Length);

                // Calculate the key from the seed
                // For MC9S12XEP100, the key calculation is specific to the security algorithm
                // For simulation, we'll use a simple algorithm based on the datasheet
                byte[] key;
                if (isMC9S12XEP100)
                {
                    // MC9S12XEP100 specific key calculation (simplified for simulation)
                    key = new byte[seed.Length];
                    for (int i = 0; i < seed.Length; i++)
                    {
                        // Rotate left by 1 and XOR with 0xA5
                        key[i] = (byte)(((seed[i] << 1) | (seed[i] >> 7)) ^ 0xA5);
                    }
                }
                else
                {
                    // Generic key calculation for other microcontrollers
                    key = new byte[seed.Length];
                    for (int i = 0; i < seed.Length; i++)
                    {
                        key[i] = (byte)(seed[i] ^ 0xFF);
                    }
                }

                // Send the key
                byte[] sendKeyRequest = new byte[3 + key.Length];
                sendKeyRequest[0] = (byte)(2 + key.Length); // Length
                sendKeyRequest[1] = CAN_SECURITY_ACCESS_COMMAND; // Security access
                sendKeyRequest[2] = 0x02; // sendKey
                Array.Copy(key, 0, sendKeyRequest, 3, key.Length);

                response = await PerformDiagnosticRequestAsync(requestId, responseId, sendKeyRequest, 2000);

                if (response == null || response.Length < 2 || response[1] != 0x67) // 0x67 is positive response to 0x27
                {
                    _logger?.LogError("Failed to send security access key", "CANProtocolHandler");
                    return null;
                }

                _logger?.LogInformation("Security access granted for flash memory read", "CANProtocolHandler");

                // Read flash memory in chunks
                // MC9S12XEP100 specific considerations:
                // - Flash memory is organized in 1KB sectors
                // - Each sector contains 128 phrases of 8 bytes each
                // - ECC is applied at the phrase level (64 data bits + 8 ECC bits)

                int chunkSize = 32; // Default chunk size for CAN
                int sectorSize = 1024; // MC9S12XEP100 sector size
                int phraseSize = 8; // MC9S12XEP100 phrase size
                int progressPercentage = 0;
                int lastReportedProgress = 0;

                if (isMC9S12XEP100)
                {
                    _logger?.LogInformation($"Using MC9S12XEP100 specific flash memory organization: {sectorSize}-byte sectors, {phraseSize}-byte phrases", "CANProtocolHandler");

                    // For MC9S12XEP100, we might need to adjust the chunk size to align with phrase boundaries
                    if (chunkSize % phraseSize != 0)
                    {
                        int adjustedChunkSize = (chunkSize / phraseSize) * phraseSize;
                        _logger?.LogInformation($"Adjusting chunk size from {chunkSize} to {adjustedChunkSize} bytes to align with phrase boundaries", "CANProtocolHandler");
                        chunkSize = adjustedChunkSize;
                    }
                }

                for (uint address = 0; address < (uint)FLASH_SIZE; address += (uint)chunkSize)
                {
                    // Calculate the number of bytes to read in this chunk
                    int bytesToRead = (int)Math.Min(chunkSize, FLASH_SIZE - address);

                    // Create a read memory by address request (service ID 0x22 for flash memory)
                    // Format: [length, service ID, memory type, address (4 bytes), size]
                    byte[] readRequest = new byte[8];
                    readRequest[0] = 0x07; // Length of the request data
                    readRequest[1] = CAN_READ_FLASH_COMMAND; // Service ID for read memory by address
                    readRequest[2] = isMC9S12XEP100 ? (byte)0x00 : (byte)0x02; // Memory type (00 = Flash for MC9S12XEP100, 02 = Flash for generic)
                    readRequest[3] = (byte)((address >> 24) & 0xFF); // Address byte 1 (MSB)
                    readRequest[4] = (byte)((address >> 16) & 0xFF); // Address byte 2
                    readRequest[5] = (byte)((address >> 8) & 0xFF);  // Address byte 3
                    readRequest[6] = (byte)(address & 0xFF);         // Address byte 4 (LSB)
                    readRequest[7] = (byte)bytesToRead;              // Number of bytes to read

                    // Send the read request and wait for a response
                    _logger?.LogInformation($"Reading flash memory chunk at address 0x{address:X8}, size {bytesToRead} bytes", "CANProtocolHandler");
                    response = await PerformDiagnosticRequestAsync(requestId, responseId, readRequest, 2000);

                    // Check if the response is valid
                    if (response == null || response.Length < 2 || response[1] != 0x62) // 0x62 is positive response to 0x22
                    {
                        _logger?.LogError($"Failed to read flash memory chunk at address 0x{address:X8}", "CANProtocolHandler");

                        // For MC9S12XEP100, we might need to handle ECC errors
                        if (isMC9S12XEP100 && eccEnabled)
                        {
                            // Check if this is an ECC error
                            byte[] checkECCError = new byte[] { 0x03, 0x22, 0xF1, 0x82 }; // Read data by identifier for ECC error status
                            byte[] eccResponse = await PerformDiagnosticRequestAsync(requestId, responseId, checkECCError, 2000);

                            if (eccResponse != null && eccResponse.Length >= 4 && eccResponse[1] == 0x62 && eccResponse[3] != 0x00)
                            {
                                _logger?.LogWarning($"ECC error detected at address 0x{address:X8}, attempting error correction", "CANProtocolHandler");

                                // Request error correction
                                byte[] correctECCError = new byte[] {
                                    0x06, // Length
                                    0x31, // Routine Control
                                    0x01, // Start Routine
                                    0xF1, 0x83, // ECC Correction Routine ID
                                    (byte)((address >> 8) & 0xFF), // Address high byte
                                    (byte)(address & 0xFF)         // Address low byte
                                };

                                byte[] correctionResponse = await PerformDiagnosticRequestAsync(requestId, responseId, correctECCError, 5000);

                                if (correctionResponse != null && correctionResponse.Length >= 2 && correctionResponse[1] == 0x71)
                                {
                                    _logger?.LogInformation($"ECC error correction successful, retrying read", "CANProtocolHandler");

                                    // Retry the read after error correction
                                    response = await PerformDiagnosticRequestAsync(requestId, responseId, readRequest, 2000);

                                    if (response == null || response.Length < 2 || response[1] != 0x62)
                                    {
                                        _logger?.LogError($"Failed to read flash memory chunk after ECC correction at address 0x{address:X8}", "CANProtocolHandler");
                                        continue;
                                    }
                                }
                                else
                                {
                                    _logger?.LogError($"ECC error correction failed at address 0x{address:X8}", "CANProtocolHandler");
                                    continue;
                                }
                            }
                            else
                            {
                                // Try again with a shorter delay
                                await Task.Delay(100);
                                response = await PerformDiagnosticRequestAsync(requestId, responseId, readRequest, 2000);

                                if (response == null || response.Length < 2 || response[1] != 0x62)
                                {
                                    _logger?.LogError($"Failed to read flash memory chunk at address 0x{address:X8} after retry", "CANProtocolHandler");
                                    continue; // Skip this chunk and continue with the next one
                                }
                            }
                        }
                        else
                        {
                            // Try again with a shorter delay
                            await Task.Delay(100);
                            response = await PerformDiagnosticRequestAsync(requestId, responseId, readRequest, 2000);

                            if (response == null || response.Length < 2 || response[1] != 0x62)
                            {
                                _logger?.LogError($"Failed to read flash memory chunk at address 0x{address:X8} after retry", "CANProtocolHandler");
                                continue; // Skip this chunk and continue with the next one
                            }
                        }
                    }

                    // Extract the data from the response (skip the first 2 bytes: length and service ID)
                    int dataOffset = 2;
                    int dataLength = response.Length - dataOffset;
                    if (dataLength > 0)
                    {
                        // Copy the data to the microcontroller code buffer
                        Array.Copy(response, dataOffset, mcuCode, bytesRead, dataLength);
                        bytesRead += dataLength;

                        // For MC9S12XEP100 with ECC enabled, verify each phrase
                        if (isMC9S12XEP100 && eccEnabled && dataLength >= phraseSize)
                        {
                            // Log only for the first phrase in the chunk to avoid excessive logging
                            _logger?.LogInformation($"Read {dataLength} bytes of flash memory at address 0x{address:X8} with ECC verification", "CANProtocolHandler");
                        }
                        else
                        {
                            _logger?.LogInformation($"Read {dataLength} bytes of flash memory at address 0x{address:X8}", "CANProtocolHandler");
                        }
                    }

                    // Calculate and report progress
                    progressPercentage = (int)((address + bytesToRead) * 100 / FLASH_SIZE);
                    if (progressPercentage > lastReportedProgress)
                    {
                        _logger?.LogInformation($"Flash memory read progress: {progressPercentage}%", "CANProtocolHandler");
                        lastReportedProgress = progressPercentage;
                    }

                    // Add a small delay between requests to avoid overwhelming the ECU
                    await Task.Delay(10);

                    // Send a tester present message every few chunks to keep the diagnostic session active
                    if (address % (chunkSize * 10) == 0 && address > 0)
                    {
                        _logger?.LogInformation("Sending tester present message to keep diagnostic session active", "CANProtocolHandler");
                        byte[] testerPresentData = new byte[] { 0x02, CAN_TESTER_PRESENT_COMMAND, 0x00 };
                        await PerformDiagnosticRequestAsync(requestId, responseId, testerPresentData, 1000);
                    }
                }

                // For MC9S12XEP100, perform additional verification if ECC is enabled
                if (isMC9S12XEP100 && eccEnabled)
                {
                    _logger?.LogInformation("Performing final ECC verification for MC9S12XEP100 flash memory", "CANProtocolHandler");

                    // Request ECC verification routine
                    byte[] verifyECC = new byte[] {
                        0x04, // Length
                        0x31, // Routine Control
                        0x01, // Start Routine
                        0xF1, 0x84 // ECC Verification Routine ID
                    };

                    byte[] verificationResponse = await PerformDiagnosticRequestAsync(requestId, responseId, verifyECC, 10000);

                    if (verificationResponse != null && verificationResponse.Length >= 2 && verificationResponse[1] == 0x71)
                    {
                        _logger?.LogInformation("ECC verification successful for MC9S12XEP100 flash memory", "CANProtocolHandler");
                    }
                    else
                    {
                        _logger?.LogWarning("ECC verification failed or not supported for MC9S12XEP100 flash memory", "CANProtocolHandler");
                    }
                }

                // Exit programming mode
                _logger?.LogInformation("Exiting programming mode", "CANProtocolHandler");
                byte[] exitProgrammingMode = new byte[] { 0x02, 0x10, 0x01 }; // Diagnostic session control, default session
                await PerformDiagnosticRequestAsync(requestId, responseId, exitProgrammingMode, 2000);

                // For simulation purposes, if we didn't read any data, generate random data
                if (bytesRead == 0)
                {
                    _logger?.LogWarning("No flash memory data read, generating simulated data", "CANProtocolHandler");
                    Random random = new Random();
                    random.NextBytes(mcuCode);
                    bytesRead = FLASH_SIZE;
                }

                _logger?.LogInformation($"Read {bytesRead} bytes of microcontroller code from ECU {ecu.Name}", "CANProtocolHandler");

                // For MC9S12XEP100, add additional information about the flash memory organization
                if (isMC9S12XEP100)
                {
                    int sectors = FLASH_SIZE / sectorSize;
                    int phrases = FLASH_SIZE / phraseSize;
                    _logger?.LogInformation($"MC9S12XEP100 flash memory organization: {sectors} sectors, {phrases} phrases, ECC {(eccEnabled ? "enabled" : "disabled")}", "CANProtocolHandler");
                }

                return mcuCode;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to read microcontroller code from ECU {ecu?.Name} via CAN", "CANProtocolHandler", ex);
                return null;
            }
        }

        /// <summary>
        /// Writes microcontroller code to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="code">The code to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public override async Task<bool> WriteMicrocontrollerCodeAsync(ECUDevice ecu, byte[] code)
        {
            try
            {
                _logger?.LogInformation($"Writing microcontroller code to ECU {ecu?.Name} via CAN", "CANProtocolHandler");

                if (!ValidateInitialization() || ecu == null || !ValidateECU(ecu))
                {
                    return false;
                }

                if (code == null || code.Length == 0)
                {
                    _logger?.LogError("Microcontroller code is null or empty", "CANProtocolHandler");
                    return false;
                }

                // Check if code size is valid for MC9S12XEP100
                if (code.Length > FLASH_SIZE)
                {
                    _logger?.LogError($"Microcontroller code size ({code.Length} bytes) exceeds flash memory size ({FLASH_SIZE} bytes)", "CANProtocolHandler");
                    return false;
                }

                // Check if code is aligned to phrase boundaries (8 bytes) for MC9S12XEP100 ECC
                if (code.Length % PHRASE_SIZE != 0)
                {
                    _logger?.LogWarning($"Microcontroller code size ({code.Length} bytes) is not aligned to phrase boundaries ({PHRASE_SIZE} bytes). Padding will be added.", "CANProtocolHandler");

                    // Pad the code to align with phrase boundaries
                    int paddingSize = PHRASE_SIZE - (code.Length % PHRASE_SIZE);
                    byte[] paddedCode = new byte[code.Length + paddingSize];
                    Array.Copy(code, paddedCode, code.Length);

                    // Fill padding with 0xFF (erased state)
                    for (int i = code.Length; i < paddedCode.Length; i++)
                    {
                        paddedCode[i] = 0xFF;
                    }

                    code = paddedCode;
                    _logger?.LogInformation($"Microcontroller code padded to {code.Length} bytes to align with phrase boundaries", "CANProtocolHandler");
                }

                if (code.Length > FLASH_SIZE)
                {
                    _logger?.LogError($"Microcontroller code size ({code.Length} bytes) exceeds maximum size ({FLASH_SIZE} bytes)", "CANProtocolHandler");
                    return false;
                }

                // First, check if this is a MC9S12XEP100 microcontroller
                bool isMC9S12XEP100 = ecu.MicrocontrollerType == "MC9S12XEP100";
                if (isMC9S12XEP100)
                {
                    _logger?.LogInformation($"ECU {ecu.Name} uses MC9S12XEP100 microcontroller, applying specific flash write protocol", "CANProtocolHandler");

                    // For MC9S12XEP100, check if ECC is enabled
                    _logger?.LogInformation($"MC9S12XEP100 ECC is {(ecu.SupportsECC ? "enabled" : "disabled")}", "CANProtocolHandler");

                    // Get the phrase size and sector size from the ECU
                    _logger?.LogInformation($"MC9S12XEP100 flash memory organization: phrase size = {ecu.FlashPhraseSize} bytes, sector size = {ecu.FlashSectorSize} bytes", "CANProtocolHandler");

                    if (ecu.SupportsECC && code.Length % ecu.FlashPhraseSize != 0)
                    {
                        _logger?.LogWarning($"Microcontroller code size ({code.Length} bytes) is not aligned to phrase boundaries ({ecu.FlashPhraseSize} bytes). Padding will be added.", "CANProtocolHandler");

                        // Pad the code to align with phrase boundaries
                        int paddingSize = ecu.FlashPhraseSize - (code.Length % ecu.FlashPhraseSize);
                        byte[] paddedCode = new byte[code.Length + paddingSize];
                        Array.Copy(code, paddedCode, code.Length);

                        // Fill padding with 0xFF (erased state)
                        for (int i = code.Length; i < paddedCode.Length; i++)
                        {
                            paddedCode[i] = 0xFF;
                        }

                        code = paddedCode;
                        _logger?.LogInformation($"Microcontroller code padded to {code.Length} bytes to align with phrase boundaries", "CANProtocolHandler");
                    }
                }
                else
                {
                    _logger?.LogInformation($"ECU {ecu.Name} does not use MC9S12XEP100 microcontroller, using generic flash write protocol", "CANProtocolHandler");
                }

                // Standard diagnostic request and response IDs
                uint requestId = 0x7E0; // Standard diagnostic request ID
                uint responseId = 0x7E8; // Standard diagnostic response ID

                // First, enter programming mode to access the flash memory
                _logger?.LogInformation("Entering programming mode to access flash memory", "CANProtocolHandler");
                byte[] enterProgrammingMode = new byte[] { 0x02, 0x10, 0x02 }; // Diagnostic session control, programming session
                byte[] response = await PerformDiagnosticRequestAsync(requestId, responseId, enterProgrammingMode, 2000);

                if (response == null || response.Length < 2 || response[1] != 0x50) // 0x50 is positive response to 0x10
                {
                    _logger?.LogError("Failed to enter programming mode", "CANProtocolHandler");
                    return false;
                }

                // For MC9S12XEP100, we need to configure the flash module before writing
                if (isMC9S12XEP100)
                {
                    _logger?.LogInformation("Configuring MC9S12XEP100 flash module for write operation", "CANProtocolHandler");

                    // Configure flash module timing parameters based on MC9S12XEP100 datasheet
                    // This involves setting the FCLKDIV register to configure the flash clock divider
                    byte[] configFlashTiming = new byte[] {
                        0x04, // Length
                        0x2E, // Write Data By Identifier
                        0xF1, 0x81, // Flash timing parameter ID
                        0x0F  // Value for 50MHz bus clock (divide by 16)
                    };

                    response = await PerformDiagnosticRequestAsync(requestId, responseId, configFlashTiming, 2000);
                    if (response == null || response.Length < 2 || response[1] != 0x6E) // 0x6E is positive response to 0x2E
                    {
                        _logger?.LogWarning("Failed to configure flash timing parameters, continuing anyway", "CANProtocolHandler");
                    }

                    // Check ECC status
                    _logger?.LogInformation("Checking ECC status before writing flash memory", "CANProtocolHandler");
                    byte[] checkECCStatus = new byte[] { 0x03, 0x22, 0xF1, 0x80 }; // Read data by identifier for ECC status
                    response = await PerformDiagnosticRequestAsync(requestId, responseId, checkECCStatus, 2000);

                    bool eccEnabled = true;
                    if (response != null && response.Length >= 4 && response[1] == 0x62)
                    {
                        eccEnabled = response[3] == 0x01;
                        _logger?.LogInformation($"ECC is {(eccEnabled ? "enabled" : "disabled")} on ECU {ecu.Name}", "CANProtocolHandler");
                    }
                    else
                    {
                        _logger?.LogWarning("Could not determine ECC status, assuming enabled", "CANProtocolHandler");
                    }

                    // If ECC is enabled, ensure code is aligned to phrase boundaries
                    if (eccEnabled && code.Length % PHRASE_SIZE != 0)
                    {
                        _logger?.LogWarning($"Microcontroller code size ({code.Length} bytes) is not aligned to phrase boundaries ({PHRASE_SIZE} bytes) for ECC. Padding will be added.", "CANProtocolHandler");

                        // Pad the code to align with phrase boundaries
                        int paddingSize = PHRASE_SIZE - (code.Length % PHRASE_SIZE);
                        byte[] paddedCode = new byte[code.Length + paddingSize];
                        Array.Copy(code, paddedCode, code.Length);

                        // Fill padding with 0xFF (erased state)
                        for (int i = code.Length; i < paddedCode.Length; i++)
                        {
                            paddedCode[i] = 0xFF;
                        }

                        code = paddedCode;
                        _logger?.LogInformation($"Microcontroller code padded to {code.Length} bytes to align with phrase boundaries for ECC", "CANProtocolHandler");
                    }
                }

                // Security access is required to write flash memory
                _logger?.LogInformation("Requesting security access for flash memory write", "CANProtocolHandler");
                byte[] securityAccessRequest = new byte[] { 0x02, CAN_SECURITY_ACCESS_COMMAND, 0x01 }; // Security access, requestSeed
                response = await PerformDiagnosticRequestAsync(requestId, responseId, securityAccessRequest, 2000);

                if (response == null || response.Length < 3 || response[1] != 0x67) // 0x67 is positive response to 0x27
                {
                    _logger?.LogError("Failed to request security access seed", "CANProtocolHandler");
                    return false;
                }

                // Extract the seed from the response (typically 2 or 4 bytes)
                byte[] seed = new byte[response.Length - 2];
                Array.Copy(response, 2, seed, 0, seed.Length);

                // Calculate the key from the seed
                // For MC9S12XEP100, the key calculation is specific to the security algorithm
                byte[] key;
                if (isMC9S12XEP100)
                {
                    // MC9S12XEP100 specific key calculation (simplified for simulation)
                    key = new byte[seed.Length];
                    for (int i = 0; i < seed.Length; i++)
                    {
                        // Rotate left by 1 and XOR with 0xA5
                        key[i] = (byte)(((seed[i] << 1) | (seed[i] >> 7)) ^ 0xA5);
                    }
                }
                else
                {
                    // Generic key calculation for other microcontrollers
                    key = new byte[seed.Length];
                    for (int i = 0; i < seed.Length; i++)
                    {
                        key[i] = (byte)(seed[i] ^ 0xFF);
                    }
                }

                // Send the key
                byte[] sendKeyRequest = new byte[3 + key.Length];
                sendKeyRequest[0] = (byte)(2 + key.Length); // Length
                sendKeyRequest[1] = CAN_SECURITY_ACCESS_COMMAND; // Security access
                sendKeyRequest[2] = 0x02; // sendKey
                Array.Copy(key, 0, sendKeyRequest, 3, key.Length);

                response = await PerformDiagnosticRequestAsync(requestId, responseId, sendKeyRequest, 2000);

                if (response == null || response.Length < 2 || response[1] != 0x67) // 0x67 is positive response to 0x27
                {
                    _logger?.LogError("Failed to send security access key", "CANProtocolHandler");
                    return false;
                }

                _logger?.LogInformation("Security access granted for flash memory write", "CANProtocolHandler");

                // For MC9S12XEP100, we need to erase the flash memory before writing
                _logger?.LogInformation("Erasing flash memory before writing", "CANProtocolHandler");

                if (isMC9S12XEP100)
                {
                    // MC9S12XEP100 specific erase procedure
                    // The MC9S12XEP100 requires erasing by sectors
                    int sectorSize = 1024; // 1KB sectors
                    int numSectors = (code.Length + sectorSize - 1) / sectorSize; // Ceiling division

                    _logger?.LogInformation($"Erasing {numSectors} sectors ({numSectors * sectorSize} bytes) for MC9S12XEP100 flash memory", "CANProtocolHandler");

                    for (int sector = 0; sector < numSectors; sector++)
                    {
                        uint sectorAddress = (uint)(sector * sectorSize);

                        // Request sector erase
                        // Format: [length, service ID, routine type, routine identifier, sector address (2 bytes)]
                        byte[] sectorEraseRequest = new byte[] {
                            0x06, // Length
                            0x31, // Routine Control service ID
                            0x01, // Start Routine
                            0xF1, 0x85, // Sector Erase Routine identifier
                            (byte)((sectorAddress >> 8) & 0xFF), // Sector address high byte
                            (byte)(sectorAddress & 0xFF)         // Sector address low byte
                        };

                        _logger?.LogInformation($"Erasing sector {sector} at address 0x{sectorAddress:X4}", "CANProtocolHandler");
                        response = await PerformDiagnosticRequestAsync(requestId, responseId, sectorEraseRequest, 5000); // Timeout for sector erase

                        if (response == null || response.Length < 2 || response[1] != 0x71) // 0x71 is positive response to 0x31
                        {
                            _logger?.LogError($"Failed to erase sector {sector} at address 0x{sectorAddress:X4}", "CANProtocolHandler");
                            return false;
                        }

                        // Send a tester present message after each sector erase to keep the session active
                        byte[] testerPresentData = new byte[] { 0x02, CAN_TESTER_PRESENT_COMMAND, 0x00 };
                        await PerformDiagnosticRequestAsync(requestId, responseId, testerPresentData, 1000);
                    }
                }
                else
                {
                    // Generic flash erase procedure for other microcontrollers
                    // Request flash erase (this is a proprietary command, may vary by manufacturer)
                    // Format: [length, service ID, erase type, memory identifier, address (4 bytes)]
                    byte[] eraseRequest = new byte[] {
                        0x07, // Length
                        0x31, // Routine Control service ID
                        0x01, // Start Routine
                        0xFF, // Erase Flash routine identifier
                        0x00, // Address byte 1 (MSB)
                        0x00, // Address byte 2
                        0x00, // Address byte 3
                        0x00  // Address byte 4 (LSB)
                    };

                    response = await PerformDiagnosticRequestAsync(requestId, responseId, eraseRequest, 10000); // Longer timeout for erase

                    if (response == null || response.Length < 2 || response[1] != 0x71) // 0x71 is positive response to 0x31
                    {
                        _logger?.LogError("Failed to erase flash memory", "CANProtocolHandler");
                        return false;
                    }
                }

                _logger?.LogInformation("Flash memory erased successfully", "CANProtocolHandler");

                // Write flash memory in chunks
                int chunkSize = 16; // Default maximum data bytes per CAN message for writing
                int phraseSize = 8; // MC9S12XEP100 phrase size
                int bytesWritten = 0;
                bool success = true;
                int progressPercentage = 0;
                int lastReportedProgress = 0;

                // For MC9S12XEP100 with ECC enabled, we need to write in phrases
                if (isMC9S12XEP100)
                {
                    // Adjust chunk size to be a multiple of phrase size
                    if (chunkSize % phraseSize != 0)
                    {
                        int adjustedChunkSize = (chunkSize / phraseSize) * phraseSize;
                        _logger?.LogInformation($"Adjusting chunk size from {chunkSize} to {adjustedChunkSize} bytes to align with phrase boundaries", "CANProtocolHandler");
                        chunkSize = adjustedChunkSize;
                    }

                    _logger?.LogInformation($"Writing MC9S12XEP100 flash memory in {phraseSize}-byte phrases, {chunkSize}-byte chunks", "CANProtocolHandler");
                }

                for (uint address = 0; address < (uint)code.Length; address += (uint)chunkSize)
                {
                    // Calculate the number of bytes to write in this chunk
                    int bytesToWrite = (int)Math.Min(chunkSize, code.Length - address);

                    // For MC9S12XEP100 with ECC, ensure we're writing complete phrases
                    if (isMC9S12XEP100 && bytesToWrite % phraseSize != 0)
                    {
                        // Adjust bytesToWrite to be a multiple of phraseSize
                        bytesToWrite = (bytesToWrite / phraseSize) * phraseSize;
                        if (bytesToWrite == 0)
                        {
                            // If we can't write a complete phrase, we're done
                            break;
                        }
                    }

                    // Create a write memory by address request (service ID 0x3C for flash memory)
                    // Format: [length, service ID, memory type, address (4 bytes), data...]
                    byte[] writeRequest = new byte[7 + bytesToWrite];
                    writeRequest[0] = (byte)(6 + bytesToWrite); // Length of the request data
                    writeRequest[1] = CAN_WRITE_FLASH_COMMAND; // Service ID for write memory by address
                    writeRequest[2] = isMC9S12XEP100 ? (byte)0x00 : (byte)0x02; // Memory type (00 = Flash for MC9S12XEP100, 02 = Flash for generic)
                    writeRequest[3] = (byte)((address >> 24) & 0xFF); // Address byte 1 (MSB)
                    writeRequest[4] = (byte)((address >> 16) & 0xFF); // Address byte 2
                    writeRequest[5] = (byte)((address >> 8) & 0xFF);  // Address byte 3
                    writeRequest[6] = (byte)(address & 0xFF);         // Address byte 4 (LSB)

                    // Copy the data to write
                    Array.Copy(code, address, writeRequest, 7, bytesToWrite);

                    // Send the write request and wait for a response
                    _logger?.LogInformation($"Writing flash memory chunk at address 0x{address:X8}, size {bytesToWrite} bytes", "CANProtocolHandler");
                    response = await PerformDiagnosticRequestAsync(requestId, responseId, writeRequest, 2000);

                    // Check if the response is valid
                    if (response == null || response.Length < 2 || response[1] != 0x7C) // 0x7C is positive response to 0x3C
                    {
                        _logger?.LogError($"Failed to write flash memory chunk at address 0x{address:X8}", "CANProtocolHandler");

                        // Try again with a shorter delay
                        await Task.Delay(100);
                        response = await PerformDiagnosticRequestAsync(requestId, responseId, writeRequest, 2000);

                        if (response == null || response.Length < 2 || response[1] != 0x7C)
                        {
                            _logger?.LogError($"Failed to write flash memory chunk at address 0x{address:X8} after retry", "CANProtocolHandler");
                            success = false;
                            break;
                        }
                    }

                    bytesWritten += bytesToWrite;

                    // Calculate and report progress
                    progressPercentage = (int)((address + bytesToWrite) * 100 / code.Length);
                    if (progressPercentage > lastReportedProgress)
                    {
                        _logger?.LogInformation($"Flash memory write progress: {progressPercentage}%", "CANProtocolHandler");
                        lastReportedProgress = progressPercentage;
                    }

                    // Add a small delay between requests to avoid overwhelming the ECU
                    await Task.Delay(20);

                    // Send a tester present message every few chunks to keep the diagnostic session active
                    if (address % (chunkSize * 10) == 0 && address > 0)
                    {
                        _logger?.LogInformation("Sending tester present message to keep diagnostic session active", "CANProtocolHandler");
                        byte[] testerPresentData = new byte[] { 0x02, CAN_TESTER_PRESENT_COMMAND, 0x00 };
                        await PerformDiagnosticRequestAsync(requestId, responseId, testerPresentData, 1000);
                    }
                }

                // Verify the written data (optional but recommended)
                if (success)
                {
                    _logger?.LogInformation("Verifying written flash memory data", "CANProtocolHandler");

                    // For MC9S12XEP100, perform ECC verification if needed
                    if (isMC9S12XEP100)
                    {
                        _logger?.LogInformation("Performing ECC verification for MC9S12XEP100 flash memory", "CANProtocolHandler");

                        // Request ECC verification routine
                        byte[] verifyECC = new byte[] {
                            0x04, // Length
                            0x31, // Routine Control
                            0x01, // Start Routine
                            0xF1, 0x84 // ECC Verification Routine ID
                        };

                        byte[] verificationResponse = await PerformDiagnosticRequestAsync(requestId, responseId, verifyECC, 10000);

                        if (verificationResponse != null && verificationResponse.Length >= 2 && verificationResponse[1] == 0x71)
                        {
                            _logger?.LogInformation("ECC verification successful for MC9S12XEP100 flash memory", "CANProtocolHandler");
                        }
                        else
                        {
                            _logger?.LogWarning("ECC verification failed or not supported for MC9S12XEP100 flash memory", "CANProtocolHandler");
                            // Continue with standard verification anyway
                        }
                    }

                    // Read back a small sample of the written data to verify
                    int verifySize = isMC9S12XEP100 ? phraseSize * 4 : 32; // For MC9S12XEP100, verify in phrases
                    const int VERIFY_SAMPLE_COUNT = 5;
                    Random random = new Random();

                    for (int i = 0; i < VERIFY_SAMPLE_COUNT; i++)
                    {
                        // Choose a random address to verify
                        uint verifyAddress;
                        if (isMC9S12XEP100)
                        {
                            // For MC9S12XEP100, ensure address is aligned to phrase boundaries
                            int maxPhrases = (int)(code.Length / phraseSize);
                            int randomPhrase = random.Next(0, maxPhrases - 4); // Ensure we have at least 4 phrases to read
                            verifyAddress = (uint)(randomPhrase * phraseSize);
                        }
                        else
                        {
                            // For other microcontrollers, any random address is fine
                            verifyAddress = (uint)random.Next(0, (int)(code.Length - verifySize));
                        }

                        // Create a read memory by address request
                        byte[] readRequest = new byte[8];
                        readRequest[0] = 0x07; // Length of the request data
                        readRequest[1] = CAN_READ_FLASH_COMMAND; // Service ID for read memory by address
                        readRequest[2] = isMC9S12XEP100 ? (byte)0x00 : (byte)0x02; // Memory type (00 = Flash for MC9S12XEP100, 02 = Flash for generic)
                        readRequest[3] = (byte)((verifyAddress >> 24) & 0xFF); // Address byte 1 (MSB)
                        readRequest[4] = (byte)((verifyAddress >> 16) & 0xFF); // Address byte 2
                        readRequest[5] = (byte)((verifyAddress >> 8) & 0xFF);  // Address byte 3
                        readRequest[6] = (byte)(verifyAddress & 0xFF);         // Address byte 4 (LSB)
                        readRequest[7] = (byte)verifySize;                     // Number of bytes to read

                        response = await PerformDiagnosticRequestAsync(requestId, responseId, readRequest, 2000);

                        if (response == null || response.Length < 2 || response[1] != 0x62) // 0x62 is positive response to 0x22
                        {
                            _logger?.LogError($"Failed to verify flash memory at address 0x{verifyAddress:X8}", "CANProtocolHandler");
                            success = false;
                            break;
                        }

                        // Extract the data from the response (skip the first 2 bytes: length and service ID)
                        int dataOffset = 2;
                        int dataLength = response.Length - dataOffset;

                        if (dataLength > 0)
                        {
                            // Compare the read data with the original data
                            bool dataMatch = true;
                            for (int j = 0; j < dataLength; j++)
                            {
                                if (response[dataOffset + j] != code[verifyAddress + j])
                                {
                                    _logger?.LogError($"Data mismatch at address 0x{verifyAddress + j:X8}: expected 0x{code[verifyAddress + j]:X2}, got 0x{response[dataOffset + j]:X2}", "CANProtocolHandler");
                                    dataMatch = false;
                                    break;
                                }
                            }

                            if (!dataMatch)
                            {
                                success = false;
                                break;
                            }

                            _logger?.LogInformation($"Verified {dataLength} bytes of flash memory at address 0x{verifyAddress:X8}", "CANProtocolHandler");
                        }
                    }
                }

                // Exit programming mode
                _logger?.LogInformation("Exiting programming mode", "CANProtocolHandler");
                byte[] exitProgrammingMode = new byte[] { 0x02, 0x10, 0x01 }; // Diagnostic session control, default session
                await PerformDiagnosticRequestAsync(requestId, responseId, exitProgrammingMode, 2000);

                if (success)
                {
                    _logger?.LogInformation($"Successfully wrote {bytesWritten} bytes of microcontroller code to ECU {ecu.Name}", "CANProtocolHandler");

                    // For MC9S12XEP100, add additional information about the flash memory organization
                    if (isMC9S12XEP100)
                    {
                        int sectorSize = 1024; // 1KB sectors
                        int sectors = (bytesWritten + sectorSize - 1) / sectorSize; // Ceiling division
                        int phrases = bytesWritten / phraseSize;
                        _logger?.LogInformation($"MC9S12XEP100 flash memory write summary: {bytesWritten} bytes, {sectors} sectors, {phrases} phrases", "CANProtocolHandler");
                    }

                    return true;
                }
                else
                {
                    _logger?.LogError($"Failed to write all microcontroller code to ECU {ecu.Name}, only wrote {bytesWritten} of {code.Length} bytes", "CANProtocolHandler");

                    // For MC9S12XEP100, add additional error information
                    if (isMC9S12XEP100)
                    {
                        int sectorSize = 1024; // 1KB sectors
                        int writtenSectors = bytesWritten / sectorSize;
                        int totalSectors = (code.Length + sectorSize - 1) / sectorSize; // Ceiling division
                        _logger?.LogError($"MC9S12XEP100 flash memory write failed: wrote {writtenSectors} of {totalSectors} sectors", "CANProtocolHandler");
                    }

                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to write microcontroller code to ECU {ecu?.Name} via CAN", "CANProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Reads active faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>List of active faults</returns>
        public override async Task<List<ECUFault>> ReadActiveFaultsAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading active faults from ECU {ecu?.Name} via CAN", "CANProtocolHandler");

                if (!ValidateInitialization() || ecu == null || !ValidateECU(ecu))
                {
                    return null;
                }

                // Read active faults from the ECU using CAN protocol
                // This would involve sending a read request and receiving the data
                // For now, we'll just simulate this
                await Task.Delay(300); // Simulate read delay

                // Create a simulated list of active faults
                List<ECUFault> activeFaults = new List<ECUFault>
                {
                    new ECUFault
                    {
                        Code = "P0100",
                        Description = "Mass or Volume Air Flow Circuit Malfunction",
                        Severity = FaultSeverity.Medium,
                        Timestamp = DateTime.Now.AddHours(-2),
                        IsActive = true
                    },
                    new ECUFault
                    {
                        Code = "P0300",
                        Description = "Random/Multiple Cylinder Misfire Detected",
                        Severity = FaultSeverity.High,
                        Timestamp = DateTime.Now.AddHours(-1),
                        IsActive = true
                    }
                };

                _logger?.LogInformation($"Read {activeFaults.Count} active faults from ECU {ecu.Name}", "CANProtocolHandler");
                return activeFaults;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to read active faults from ECU {ecu?.Name} via CAN", "CANProtocolHandler", ex);
                return null;
            }
        }

        /// <summary>
        /// Reads inactive faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>List of inactive faults</returns>
        public override async Task<List<ECUFault>> ReadInactiveFaultsAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading inactive faults from ECU {ecu?.Name} via CAN", "CANProtocolHandler");

                if (!ValidateInitialization() || ecu == null || !ValidateECU(ecu))
                {
                    return null;
                }

                // Read inactive faults from the ECU using CAN protocol
                // This would involve sending a read request and receiving the data
                // For now, we'll just simulate this
                await Task.Delay(300); // Simulate read delay

                // Create a simulated list of inactive faults
                List<ECUFault> inactiveFaults = new List<ECUFault>
                {
                    new ECUFault
                    {
                        Code = "P0171",
                        Description = "System Too Lean (Bank 1)",
                        Severity = FaultSeverity.Low,
                        Timestamp = DateTime.Now.AddDays(-5),
                        IsActive = false
                    },
                    new ECUFault
                    {
                        Code = "P0420",
                        Description = "Catalyst System Efficiency Below Threshold (Bank 1)",
                        Severity = FaultSeverity.Medium,
                        Timestamp = DateTime.Now.AddDays(-3),
                        IsActive = false
                    }
                };

                _logger?.LogInformation($"Read {inactiveFaults.Count} inactive faults from ECU {ecu.Name}", "CANProtocolHandler");
                return inactiveFaults;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to read inactive faults from ECU {ecu?.Name} via CAN", "CANProtocolHandler", ex);
                return null;
            }
        }

        /// <summary>
        /// Clears faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to clear faults from</param>
        /// <returns>True if clearing is successful, false otherwise</returns>
        public override async Task<bool> ClearFaultsAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Clearing faults from ECU {ecu?.Name} via CAN", "CANProtocolHandler");

                if (!ValidateInitialization() || ecu == null || !ValidateECU(ecu))
                {
                    return false;
                }

                // Clear faults from the ECU using CAN protocol
                // This would involve sending a clear request
                // For now, we'll just simulate this
                await Task.Delay(200); // Simulate clear delay

                // Standard diagnostic request and response IDs
                uint requestId = 0x7E0; // Standard diagnostic request ID
                uint responseId = 0x7E8; // Standard diagnostic response ID

                // Create a clear faults request
                byte[] clearFaultsRequest = new byte[] { 0x01, CAN_CLEAR_FAULTS_COMMAND };

                // Send the clear faults request and wait for a response
                byte[] response = await PerformDiagnosticRequestAsync(requestId, responseId, clearFaultsRequest, 1000);

                // Check if the response is valid
                if (response == null || response.Length < 2 || response[1] != 0x54) // 0x54 is positive response to 0x14
                {
                    _logger?.LogError($"Failed to clear faults from ECU {ecu.Name}", "CANProtocolHandler");
                    return false;
                }

                // Clear the faults in the ECU object
                ecu.ActiveFaults.Clear();
                ecu.InactiveFaults.Clear();

                _logger?.LogInformation($"Cleared faults from ECU {ecu.Name}", "CANProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to clear faults from ECU {ecu?.Name} via CAN", "CANProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Clears all faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to clear faults from</param>
        /// <returns>True if clearing all faults is successful, false otherwise</returns>
        public override async Task<bool> ClearAllFaultsAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Clearing all faults from ECU {ecu?.Name} via CAN", "CANProtocolHandler");

                if (!ValidateInitialization() || ecu == null || !ValidateECU(ecu))
                {
                    return false;
                }

                // Standard diagnostic request and response IDs
                uint requestId = 0x7E0; // Standard diagnostic request ID
                uint responseId = 0x7E8; // Standard diagnostic response ID

                // Create a clear all faults request with specific parameters
                // The format is based on the UDS (Unified Diagnostic Services) protocol
                byte[] clearAllFaultsRequest = new byte[] {
                    0x04,                       // Length
                    CAN_CLEAR_FAULTS_COMMAND,   // Service ID for clear diagnostic information
                    0xFF,                       // Group of DTC = all
                    0xFF,                       // Memory = all
                    0xFF                        // Extended parameters = all
                };

                // Send the clear all faults request and wait for a response
                byte[] response = await PerformDiagnosticRequestAsync(requestId, responseId, clearAllFaultsRequest, 1500);

                // Check if the response is valid
                if (response == null || response.Length < 2 || response[1] != 0x54) // 0x54 is positive response to 0x14
                {
                    _logger?.LogError($"Failed to clear all faults from ECU {ecu.Name}", "CANProtocolHandler");
                    return false;
                }

                // Clear the faults in the ECU object
                ecu.ActiveFaults.Clear();
                ecu.InactiveFaults.Clear();

                _logger?.LogInformation($"Cleared all faults from ECU {ecu.Name}", "CANProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to clear all faults from ECU {ecu?.Name} via CAN", "CANProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Clears specific faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to clear faults from</param>
        /// <param name="faultCodes">The specific fault codes to clear</param>
        /// <returns>True if clearing specific faults is successful, false otherwise</returns>
        public override async Task<bool> ClearSpecificFaultsAsync(ECUDevice ecu, List<string> faultCodes)
        {
            try
            {
                _logger?.LogInformation($"Clearing specific faults from ECU {ecu?.Name} via CAN", "CANProtocolHandler");

                if (!ValidateInitialization() || ecu == null || !ValidateECU(ecu))
                {
                    return false;
                }

                if (faultCodes == null || faultCodes.Count == 0)
                {
                    _logger?.LogError("Fault codes list is null or empty", "CANProtocolHandler");
                    return false;
                }

                // Standard diagnostic request and response IDs
                uint requestId = 0x7E0; // Standard diagnostic request ID
                uint responseId = 0x7E8; // Standard diagnostic response ID

                bool allSuccessful = true;

                // Process each fault code individually
                foreach (string faultCode in faultCodes)
                {
                    _logger?.LogInformation($"Clearing fault code {faultCode} from ECU {ecu.Name}", "CANProtocolHandler");

                    // Convert the fault code string to bytes
                    // Fault codes are typically in the format "P0123", "C0456", etc.
                    // The first character indicates the system:
                    // P = Powertrain, C = Chassis, B = Body, U = Network
                    byte systemByte = 0x00;
                    if (faultCode.StartsWith("P", StringComparison.OrdinalIgnoreCase))
                        systemByte = 0x00; // Powertrain
                    else if (faultCode.StartsWith("C", StringComparison.OrdinalIgnoreCase))
                        systemByte = 0x01; // Chassis
                    else if (faultCode.StartsWith("B", StringComparison.OrdinalIgnoreCase))
                        systemByte = 0x02; // Body
                    else if (faultCode.StartsWith("U", StringComparison.OrdinalIgnoreCase))
                        systemByte = 0x03; // Network

                    // Extract the numeric part of the fault code
                    string numericPart = faultCode.Substring(1);
                    if (!uint.TryParse(numericPart, System.Globalization.NumberStyles.HexNumber, null, out uint faultValue))
                    {
                        _logger?.LogError($"Invalid fault code format: {faultCode}", "CANProtocolHandler");
                        allSuccessful = false;
                        continue;
                    }

                    // Create a clear specific fault request
                    byte[] clearSpecificFaultRequest = new byte[] {
                        0x05,                       // Length
                        CAN_CLEAR_FAULTS_COMMAND,   // Service ID for clear diagnostic information
                        systemByte,                 // System byte
                        (byte)((faultValue >> 8) & 0xFF), // High byte of fault code
                        (byte)(faultValue & 0xFF),  // Low byte of fault code
                        0x00                        // Extended parameters = none
                    };

                    // Send the clear specific fault request and wait for a response
                    byte[] response = await PerformDiagnosticRequestAsync(requestId, responseId, clearSpecificFaultRequest, 1000);

                    // Check if the response is valid
                    if (response == null || response.Length < 2 || response[1] != 0x54) // 0x54 is positive response to 0x14
                    {
                        _logger?.LogError($"Failed to clear fault code {faultCode} from ECU {ecu.Name}", "CANProtocolHandler");
                        allSuccessful = false;
                    }
                    else
                    {
                        _logger?.LogInformation($"Cleared fault code {faultCode} from ECU {ecu.Name}", "CANProtocolHandler");

                        // Remove the fault from the ECU object if it exists
                        ecu.ActiveFaults.RemoveAll(f => f.Code.Equals(faultCode, StringComparison.OrdinalIgnoreCase));
                        ecu.InactiveFaults.RemoveAll(f => f.Code.Equals(faultCode, StringComparison.OrdinalIgnoreCase));
                    }

                    // Add a small delay between requests to avoid overwhelming the ECU
                    await Task.Delay(50);
                }

                if (allSuccessful)
                {
                    _logger?.LogInformation($"Successfully cleared all specified fault codes from ECU {ecu.Name}", "CANProtocolHandler");
                }
                else
                {
                    _logger?.LogWarning($"Failed to clear some fault codes from ECU {ecu.Name}", "CANProtocolHandler");
                }

                return allSuccessful;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to clear specific faults from ECU {ecu?.Name} via CAN", "CANProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Reads parameters from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>Dictionary of parameter names and values</returns>
        public override async Task<Dictionary<string, object>> ReadParametersAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading parameters from ECU {ecu?.Name} via CAN", "CANProtocolHandler");

                if (!ValidateInitialization() || ecu == null || !ValidateECU(ecu))
                {
                    return null;
                }

                // Read parameters from the ECU using CAN protocol
                // This would involve sending a read request and receiving the data
                // For now, we'll just simulate this
                await Task.Delay(400); // Simulate read delay

                // Create a simulated dictionary of parameters
                Dictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "EngineRPM", 1500 },
                    { "VehicleSpeed", 65.5 },
                    { "CoolantTemp", 87 },
                    { "IntakeAirTemp", 22 },
                    { "ThrottlePosition", 25.3 },
                    { "BatteryVoltage", 12.7 },
                    { "FuelLevel", 75 },
                    { "EngineLoad", 45.2 }
                };

                _logger?.LogInformation($"Read {parameters.Count} parameters from ECU {ecu.Name}", "CANProtocolHandler");
                return parameters;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to read parameters from ECU {ecu?.Name} via CAN", "CANProtocolHandler", ex);
                return null;
            }
        }

        /// <summary>
        /// Writes parameters to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="parameters">The parameters to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public override async Task<bool> WriteParametersAsync(ECUDevice ecu, Dictionary<string, object> parameters)
        {
            try
            {
                _logger?.LogInformation($"Writing parameters to ECU {ecu?.Name} via CAN", "CANProtocolHandler");

                if (!ValidateInitialization() || ecu == null || !ValidateECU(ecu))
                {
                    return false;
                }

                if (parameters == null || parameters.Count == 0)
                {
                    _logger?.LogError("Parameters are null or empty", "CANProtocolHandler");
                    return false;
                }

                // Write parameters to the ECU using CAN protocol
                // This would involve sending a write request with the parameters
                // For now, we'll just simulate this
                await Task.Delay(500); // Simulate write delay

                // Update the parameters in the ECU object
                foreach (var parameter in parameters)
                {
                    if (ecu.Parameters.ContainsKey(parameter.Key))
                    {
                        ecu.Parameters[parameter.Key] = parameter.Value;
                    }
                    else
                    {
                        ecu.Parameters.Add(parameter.Key, parameter.Value);
                    }
                }

                _logger?.LogInformation($"Wrote {parameters.Count} parameters to ECU {ecu.Name}", "CANProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to write parameters to ECU {ecu?.Name} via CAN", "CANProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Performs a diagnostic session on an ECU
        /// </summary>
        /// <param name="ecu">The ECU to diagnose</param>
        /// <returns>Diagnostic data</returns>
        public override async Task<DiagnosticData> PerformDiagnosticSessionAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Performing diagnostic session on ECU {ecu?.Name} via CAN", "CANProtocolHandler");

                if (!ValidateInitialization() || ecu == null || !ValidateECU(ecu))
                {
                    return null;
                }

                // Perform a diagnostic session on the ECU using CAN protocol
                // This would involve sending a diagnostic request and receiving the data
                // For now, we'll just simulate this
                await Task.Delay(1000); // Simulate diagnostic delay

                // Read active and inactive faults
                List<ECUFault> activeFaults = await ReadActiveFaultsAsync(ecu);
                List<ECUFault> inactiveFaults = await ReadInactiveFaultsAsync(ecu);

                // Read parameters
                Dictionary<string, object> parameters = await ReadParametersAsync(ecu);

                // Create a diagnostic data object
                DiagnosticData diagnosticData = new DiagnosticData
                {
                    ECUId = ecu.Id,
                    ECUName = ecu.Name,
                    Timestamp = DateTime.Now,
                    ActiveFaults = activeFaults,
                    InactiveFaults = inactiveFaults,
                    Parameters = parameters,
                    OperatingMode = _currentOperatingMode,
                    ConnectionType = _vocomService.CurrentDevice.ConnectionType,
                    IsSuccessful = true,
                    SessionDurationMs = 1000 // Simulated duration
                };

                _logger?.LogInformation($"Performed diagnostic session on ECU {ecu.Name}", "CANProtocolHandler");
                return diagnosticData;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to perform diagnostic session on ECU {ecu?.Name} via CAN", "CANProtocolHandler", ex);
                return new DiagnosticData
                {
                    ECUId = ecu?.Id,
                    ECUName = ecu?.Name,
                    Timestamp = DateTime.Now,
                    IsSuccessful = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// Cancels the current operation
        /// </summary>
        /// <returns>True if cancellation is successful, false otherwise</returns>
        public override async Task<bool> CancelOperationAsync()
        {
            try
            {
                _logger?.LogInformation($"Cancelling current operation for CAN protocol handler", "CANProtocolHandler");

                if (!ValidateInitialization())
                {
                    return false;
                }

                // Implement CAN-specific cancellation logic
                // This would involve sending a cancel command or resetting the CAN controller

                // Standard diagnostic request and response IDs
                uint requestId = 0x7E0; // Standard diagnostic request ID
                uint responseId = 0x7E8; // Standard diagnostic response ID

                // Create a cancel operation request (UDS service 0x7F - negative response)
                byte[] cancelRequest = new byte[] {
                    0x03,                       // Length
                    0x7F,                       // Negative response service ID
                    0x00,                       // Current service ID (0 = all)
                    0x31                        // Response code (31 = requestOutOfRange)
                };

                // Send the cancel request
                bool sent = await SendCANMessageAsync(requestId, cancelRequest);
                if (!sent)
                {
                    _logger?.LogError("Failed to send cancel request", "CANProtocolHandler");
                    return false;
                }

                // Reset CAN controller to a known state
                _logger?.LogInformation("Resetting CAN controller to a known state", "CANProtocolHandler");

                // Request initialization mode
                _logger?.LogInformation("Requesting CAN initialization mode for reset", "CANProtocolHandler");
                // In a real implementation, this would involve writing to the CAN0_CTL0 register
                // WriteRegister(CAN0_CTL0, CAN0_CTL0_INITRQ);
                await Task.Delay(10); // Simulate register write delay

                // Wait for initialization mode to be active
                _logger?.LogInformation("Waiting for CAN initialization mode to be active", "CANProtocolHandler");
                // In a real implementation, this would involve polling the CAN0_CTL0 register
                // while ((ReadRegister(CAN0_CTL0) & CAN0_CTL0_INITRQ) == 0) { await Task.Delay(1); }
                await Task.Delay(20); // Simulate polling delay

                // Reset CAN controller registers
                _logger?.LogInformation("Resetting CAN controller registers", "CANProtocolHandler");
                // In a real implementation, this would involve writing to various CAN registers
                await Task.Delay(30); // Simulate register write delay

                // Exit initialization mode
                _logger?.LogInformation("Exiting CAN initialization mode", "CANProtocolHandler");
                // In a real implementation, this would involve writing to the CAN0_CTL0 register
                // WriteRegister(CAN0_CTL0, 0x00);
                await Task.Delay(10); // Simulate register write delay

                // Send a diagnostic session control message to establish default session
                byte[] sessionControlData = new byte[] { 0x02, CAN_DIAGNOSTIC_SESSION_COMMAND, 0x01 }; // Default session
                await PerformDiagnosticRequestAsync(requestId, responseId, sessionControlData, 1000);

                _logger?.LogInformation("Operation cancelled for CAN protocol handler", "CANProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to cancel operation for CAN protocol handler", "CANProtocolHandler", ex);
                return false;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Sets the CAN communication speed (high or low)
        /// </summary>
        /// <param name="highSpeed">True for high-speed, false for low-speed</param>
        /// <returns>True if successful, false otherwise</returns>
        private async Task<bool> SetCommunicationSpeedAsync(bool highSpeed)
        {
            try
            {
                // Use default configuration instead of creating a new factory
                var canConfig = GetDefaultCANConfiguration();

                // Get the baud rates from the configuration
                int highSpeedBaudRate = 500000; // Default value
                int lowSpeedBaudRate = 125000; // Default value

                if (canConfig.TryGetValue("HighSpeedBaudRate", out object highSpeedObj) && highSpeedObj is int highSpeed_)
                {
                    highSpeedBaudRate = highSpeed_;
                }

                if (canConfig.TryGetValue("LowSpeedBaudRate", out object lowSpeedObj) && lowSpeedObj is int lowSpeed_)
                {
                    lowSpeedBaudRate = lowSpeed_;
                }

                int baudRate = highSpeed ? highSpeedBaudRate : lowSpeedBaudRate;
                _logger?.LogInformation($"Setting CAN communication speed to {(highSpeed ? "high-speed" : "low-speed")} ({baudRate} bps)", "CANProtocolHandler");

                if (!ValidateInitialization())
                {
                    return false;
                }

                // Request initialization mode
                _logger?.LogInformation("Requesting CAN initialization mode", "CANProtocolHandler");
                // In a real implementation, this would involve writing to the CAN0_CTL0 register
                // WriteRegister(CAN0_CTL0, CAN0_CTL0_INITRQ);
                await Task.Delay(10); // Simulate register write delay

                // Wait for initialization mode to be active
                _logger?.LogInformation("Waiting for CAN initialization mode to be active", "CANProtocolHandler");
                // In a real implementation, this would involve polling the CAN0_CTL0 register
                // while ((ReadRegister(CAN0_CTL0) & CAN0_CTL0_INITRQ) == 0) { await Task.Delay(1); }
                await Task.Delay(20); // Simulate polling delay

                // Calculate the appropriate BTR0 and BTR1 values based on the baud rate
                // For MC9S12XEP100 with 50 MHz clock:
                // For 500 kbps: BTR0 = 0x01 (Prescaler = 2), BTR1 = 0x1C (TSEG1 = 4, TSEG2 = 3, SJW = 1)
                byte btr0 = 0x01; // Default for 500 kbps
                byte btr1 = 0x1C; // Default for 500 kbps

                if (baudRate == 125000)
                {
                    btr0 = 0x07; // Prescaler = 8
                    btr1 = 0x1C; // TSEG1 = 4, TSEG2 = 3, SJW = 1
                }
                else if (baudRate == 250000)
                {
                    btr0 = 0x03; // Prescaler = 4
                    btr1 = 0x1C; // TSEG1 = 4, TSEG2 = 3, SJW = 1
                }
                else if (baudRate == 1000000)
                {
                    btr0 = 0x00; // Prescaler = 1
                    btr1 = 0x1C; // TSEG1 = 4, TSEG2 = 3, SJW = 1
                }

                _logger?.LogInformation($"Configuring CAN bus timing registers for {baudRate} bps communication (BTR0=0x{btr0:X2}, BTR1=0x{btr1:X2})", "CANProtocolHandler");
                // In a real implementation, this would involve writing to the CAN0_BTR0 and CAN0_BTR1 registers
                // WriteRegister(CAN0_BTR0, btr0);
                // WriteRegister(CAN0_BTR1, btr1);
                await Task.Delay(10); // Simulate register write delay

                // Exit initialization mode
                _logger?.LogInformation("Exiting CAN initialization mode", "CANProtocolHandler");
                // In a real implementation, this would involve writing to the CAN0_CTL0 register
                // WriteRegister(CAN0_CTL0, 0x00);
                await Task.Delay(10); // Simulate register write delay

                // Wait for initialization mode to be inactive
                _logger?.LogInformation("Waiting for CAN initialization mode to be inactive", "CANProtocolHandler");
                // In a real implementation, this would involve polling the CAN0_CTL0 register
                // while ((ReadRegister(CAN0_CTL0) & CAN0_CTL0_INITRQ) != 0) { await Task.Delay(1); }
                await Task.Delay(20); // Simulate polling delay

                _logger?.LogInformation($"CAN communication speed set to {(highSpeed ? "high-speed (500 kbps)" : "low-speed (125 kbps)")}", "CANProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to set CAN communication speed: {ex.Message}", "CANProtocolHandler");
                return false;
            }
        }

        /// <summary>
        /// Sends a CAN message
        /// </summary>
        /// <param name="id">The CAN message ID</param>
        /// <param name="data">The CAN message data</param>
        /// <param name="isExtendedId">Whether the ID is an extended ID (29-bit) or standard ID (11-bit)</param>
        /// <returns>True if successful, false otherwise</returns>
        private async Task<bool> SendCANMessageInternalAsync(uint id, byte[] data, bool isExtendedId = false)
        {
            try
            {
                _logger?.LogInformation($"Sending CAN message with ID 0x{id:X} and {data?.Length ?? 0} bytes of data", "CANProtocolHandler");

                if (!ValidateInitialization())
                {
                    return false;
                }

                if (data == null)
                {
                    _logger?.LogError("CAN message data is null", "CANProtocolHandler");
                    return false;
                }

                if (data.Length > 8)
                {
                    _logger?.LogError($"CAN message data length ({data.Length} bytes) exceeds maximum length (8 bytes)", "CANProtocolHandler");
                    return false;
                }

                // Wait for a transmit buffer to be available
                _logger?.LogInformation("Waiting for a transmit buffer to be available", "CANProtocolHandler");
                // In a real implementation, this would involve polling the CAN0_TFLG register
                // while ((ReadRegister(CAN0_TFLG) & 0x01) == 0) { await Task.Delay(1); }
                await Task.Delay(10); // Simulate polling delay

                // Select the transmit buffer
                _logger?.LogInformation("Selecting the transmit buffer", "CANProtocolHandler");
                // In a real implementation, this would involve writing to the CAN0_TBSEL register
                // WriteRegister(CAN0_TBSEL, 0x01);
                await Task.Delay(5); // Simulate register write delay

                // Write the message ID
                _logger?.LogInformation($"Writing message ID 0x{id:X}", "CANProtocolHandler");
                // In a real implementation, this would involve writing to the CAN0_TXFG_ID0, CAN0_TXFG_ID1, etc. registers
                // if (isExtendedId)
                // {
                //     // Extended ID (29-bit)
                //     WriteRegister(CAN0_TXFG_ID0, (byte)((id >> 21) & 0xFF));
                //     WriteRegister(CAN0_TXFG_ID1, (byte)((id >> 13) & 0xFF));
                //     WriteRegister(CAN0_TXFG_ID2, (byte)((id >> 5) & 0xFF));
                //     WriteRegister(CAN0_TXFG_ID3, (byte)((id << 3) & 0xF8));
                // }
                // else
                // {
                //     // Standard ID (11-bit)
                //     WriteRegister(CAN0_TXFG_ID0, (byte)((id >> 3) & 0xFF));
                //     WriteRegister(CAN0_TXFG_ID1, (byte)((id << 5) & 0xE0));
                // }
                await Task.Delay(5); // Simulate register write delay

                // Write the data length code
                _logger?.LogInformation($"Writing data length code {data.Length}", "CANProtocolHandler");
                // In a real implementation, this would involve writing to the CAN0_TXFG_DLC register
                // byte dlc = (byte)(data.Length | (isExtendedId ? 0x10 : 0x00)); // Set IDE bit for extended ID
                // WriteRegister(CAN0_TXFG_DLC, dlc);
                await Task.Delay(5); // Simulate register write delay

                // Write the data bytes
                _logger?.LogInformation("Writing data bytes", "CANProtocolHandler");
                // In a real implementation, this would involve writing to the CAN0_TXFG_DATA0, CAN0_TXFG_DATA1, etc. registers
                // for (int i = 0; i < data.Length; i++)
                // {
                //     WriteRegister((uint)(CAN0_TXFG_DATA0 + i), data[i]);
                // }
                await Task.Delay(5); // Simulate register write delay

                // Transmit the message
                _logger?.LogInformation("Transmitting the message", "CANProtocolHandler");
                // In a real implementation, this would involve writing to the CAN0_TFLG register
                // WriteRegister(CAN0_TFLG, 0x01);
                await Task.Delay(10); // Simulate register write delay

                _logger?.LogInformation($"CAN message with ID 0x{id:X} sent successfully", "CANProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to send CAN message: {ex.Message}", "CANProtocolHandler");
                return false;
            }
        }

        /// <summary>
        /// Receives a CAN message
        /// </summary>
        /// <param name="timeout">The timeout in milliseconds</param>
        /// <returns>The received CAN message, or null if no message was received</returns>
        private async Task<(uint Id, byte[] Data, bool IsExtendedId)> ReceiveCANMessageInternalAsync(int timeout = 1000)
        {
            try
            {
                _logger?.LogInformation($"Waiting for CAN message with timeout {timeout} ms", "CANProtocolHandler");

                if (!ValidateInitialization())
                {
                    return (0, null, false);
                }

                // Wait for a message to be received
                _logger?.LogInformation("Waiting for a message to be received", "CANProtocolHandler");
                // In a real implementation, this would involve polling the CAN0_RFLG register with a timeout
                // DateTime startTime = DateTime.Now;
                // bool messageReceived = false;
                // while ((DateTime.Now - startTime).TotalMilliseconds < timeout)
                // {
                //     if ((ReadRegister(CAN0_RFLG) & 0x01) != 0)
                //     {
                //         messageReceived = true;
                //         break;
                //     }
                //     await Task.Delay(1);
                // }
                // if (!messageReceived)
                // {
                //     _logger?.LogWarning("No CAN message received within timeout", "CANProtocolHandler");
                //     return (0, null, false);
                // }
                await Task.Delay(timeout / 10); // Simulate polling delay (reduced for simulation)

                // For simulation, we'll just create a dummy message
                uint id = 0x7E0; // Standard diagnostic request ID
                bool isExtendedId = false;
                byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05 };

                // In a real implementation, we would read the message ID, data length code, and data bytes
                // bool isExtendedId = (ReadRegister(CAN0_RXFG_DLC) & 0x10) != 0;
                // uint id;
                // if (isExtendedId)
                // {
                //     // Extended ID (29-bit)
                //     id = (uint)((ReadRegister(CAN0_RXFG_ID0) << 21) |
                //                 (ReadRegister(CAN0_RXFG_ID1) << 13) |
                //                 (ReadRegister(CAN0_RXFG_ID2) << 5) |
                //                 (ReadRegister(CAN0_RXFG_ID3) >> 3));
                // }
                // else
                // {
                //     // Standard ID (11-bit)
                //     id = (uint)((ReadRegister(CAN0_RXFG_ID0) << 3) |
                //                 (ReadRegister(CAN0_RXFG_ID1) >> 5));
                // }
                // byte dlc = (byte)(ReadRegister(CAN0_RXFG_DLC) & 0x0F);
                // byte[] data = new byte[dlc];
                // for (int i = 0; i < dlc; i++)
                // {
                //     data[i] = ReadRegister((uint)(CAN0_RXFG_DATA0 + i));
                // }

                // Clear the receive flag
                _logger?.LogInformation("Clearing the receive flag", "CANProtocolHandler");
                // In a real implementation, this would involve writing to the CAN0_RFLG register
                // WriteRegister(CAN0_RFLG, 0x01);
                await Task.Delay(5); // Simulate register write delay

                _logger?.LogInformation($"Received CAN message with ID 0x{id:X} and {data.Length} bytes of data", "CANProtocolHandler");
                return (id, data, isExtendedId);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to receive CAN message: {ex.Message}", "CANProtocolHandler");
                return (0, null, false);
            }
        }

        /// <summary>
        /// Performs a CAN diagnostic request and waits for a response
        /// </summary>
        /// <param name="requestId">The request message ID</param>
        /// <param name="responseId">The expected response message ID</param>
        /// <param name="data">The request data</param>
        /// <param name="timeout">The timeout in milliseconds</param>
        /// <returns>The response data, or null if no response was received</returns>
        private async Task<byte[]> PerformDiagnosticRequestInternalAsync(uint requestId, uint responseId, byte[] data, int timeout = 1000)
        {
            try
            {
                _logger?.LogInformation($"Performing diagnostic request with ID 0x{requestId:X}", "CANProtocolHandler");

                if (!ValidateInitialization())
                {
                    return null;
                }

                // Send the request
                bool sent = await SendCANMessageInternalAsync(requestId, data);
                if (!sent)
                {
                    _logger?.LogError("Failed to send diagnostic request", "CANProtocolHandler");
                    return null;
                }

                // Wait for the response
                DateTime startTime = DateTime.Now;
                while ((DateTime.Now - startTime).TotalMilliseconds < timeout)
                {
                    var (id, responseData, _) = await ReceiveCANMessageInternalAsync(100);
                    if (id == responseId && responseData != null && responseData.Length > 0)
                    {
                        _logger?.LogInformation($"Received diagnostic response with ID 0x{id:X} and {responseData.Length} bytes of data", "CANProtocolHandler");
                        return responseData;
                    }
                }

                _logger?.LogWarning($"No diagnostic response received within timeout {timeout} ms", "CANProtocolHandler");
                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to perform diagnostic request: {ex.Message}", "CANProtocolHandler");
                return null;
            }
        }

        #endregion

        #region Data Communication Methods

        /// <summary>
        /// Reads data from an ECU using the specified command
        /// </summary>
        /// <param name="device">The Vocom device to use</param>
        /// <param name="ecu">The ECU to read from</param>
        /// <param name="command">The command to send</param>
        /// <param name="dataSize">The size of data to read</param>
        /// <returns>The data read from the ECU</returns>
        public override async Task<byte[]> ReadDataAsync(VocomDevice device, ECUDevice ecu, byte command, int dataSize)
        {
            try
            {
                _logger?.LogInformation($"Reading data from ECU {ecu?.Name} with command 0x{command:X2} via CAN", "CANProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return null;
                }

                if (device == null)
                {
                    _logger?.LogError("Vocom device is null", "CANProtocolHandler");
                    return null;
                }

                // Get the CAN ID for the ECU
                uint canId = 0x7E0; // Default diagnostic request ID
                if (ecu.Properties.ContainsKey("CANId") && ecu.Properties["CANId"] is uint ecuCanId)
                {
                    canId = ecuCanId;
                }
                else if (!string.IsNullOrEmpty(ecu.CANId))
                {
                    // Try to parse the CAN ID from the string property
                    if (uint.TryParse(ecu.CANId, out uint parsedCanId))
                    {
                        canId = parsedCanId;
                    }
                }

                // Prepare the command data
                byte[] commandData = new byte[] { command };

                // Send the CAN frame and get the response
                byte[] response = await _vocomService.SendCANFrameAsync(device, canId, commandData, dataSize);
                if (response == null || response.Length == 0)
                {
                    _logger?.LogError($"Failed to read data from ECU {ecu.Name} via CAN", "CANProtocolHandler");
                    return null;
                }

                _logger?.LogInformation($"Read {response.Length} bytes of data from ECU {ecu.Name} via CAN", "CANProtocolHandler");
                return response;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading data from ECU {ecu?.Name} via CAN: {ex.Message}", "CANProtocolHandler");
                return null;
            }
        }

        /// <summary>
        /// Writes data to an ECU using the specified command
        /// </summary>
        /// <param name="device">The Vocom device to use</param>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="command">The command to send</param>
        /// <param name="data">The data to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public override async Task<bool> WriteDataAsync(VocomDevice device, ECUDevice ecu, byte command, byte[] data)
        {
            try
            {
                _logger?.LogInformation($"Writing data to ECU {ecu?.Name} with command 0x{command:X2} via CAN", "CANProtocolHandler");

                if (!ValidateInitialization() || ecu == null || !ValidateECU(ecu))
                {
                    return false;
                }

                if (device == null)
                {
                    _logger?.LogError("Vocom device is null", "CANProtocolHandler");
                    return false;
                }

                if (data == null)
                {
                    _logger?.LogError("Data is null", "CANProtocolHandler");
                    return false;
                }

                // Get the CAN ID for the ECU
                uint canId = 0x7E0; // Default diagnostic request ID
                if (ecu.Properties.ContainsKey("CANId") && ecu.Properties["CANId"] is uint ecuCanId)
                {
                    canId = ecuCanId;
                }
                else if (!string.IsNullOrEmpty(ecu.CANId))
                {
                    // Try to parse the CAN ID from the string property
                    if (uint.TryParse(ecu.CANId, out uint parsedCanId))
                    {
                        canId = parsedCanId;
                    }
                }

                // Prepare the command data with the command byte followed by the data
                byte[] commandData = new byte[data.Length + 1];
                commandData[0] = command;
                Array.Copy(data, 0, commandData, 1, data.Length);

                // Send the CAN frame and get the response
                byte[] response = await _vocomService.SendCANFrameAsync(device, canId, commandData, 0);
                if (response == null || response.Length == 0 || response[0] != 0)
                {
                    _logger?.LogError($"Failed to write data to ECU {ecu.Name} via CAN", "CANProtocolHandler");
                    return false;
                }

                _logger?.LogInformation($"Wrote {data.Length} bytes of data to ECU {ecu.Name} via CAN", "CANProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing data to ECU {ecu?.Name} via CAN: {ex.Message}", "CANProtocolHandler");
                return false;
            }
        }

        /// <summary>
        /// Sets the communication speed mode (High or Low)
        /// </summary>
        /// <param name="ecu">The ECU to set the speed mode for</param>
        /// <param name="speedMode">The speed mode to set</param>
        /// <returns>True if speed mode change is successful, false otherwise</returns>
        public override async Task<bool> SetCommunicationSpeedModeAsync(ECUDevice ecu, CommunicationSpeedMode speedMode)
        {
            try
            {
                _logger?.LogInformation($"Setting communication speed mode to {speedMode} for ECU {ecu?.Name} via CAN", "CANProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return false;
                }

                // Check if the ECU supports the requested speed mode
                if (speedMode == CommunicationSpeedMode.High && !ecu.SupportsHighSpeedCommunication)
                {
                    _logger?.LogError($"ECU {ecu.Name} does not support high-speed CAN communication", "CANProtocolHandler");
                    return false;
                }
                else if (speedMode == CommunicationSpeedMode.Low && !ecu.SupportsLowSpeedCommunication)
                {
                    _logger?.LogError($"ECU {ecu.Name} does not support low-speed CAN communication", "CANProtocolHandler");
                    return false;
                }

                // Set the communication speed
                bool highSpeed = speedMode == CommunicationSpeedMode.High;
                bool speedSet = await SetCommunicationSpeedAsync(highSpeed);
                if (!speedSet)
                {
                    _logger?.LogError($"Failed to set CAN communication speed to {speedMode}", "CANProtocolHandler");
                    return false;
                }

                // Update the ECU's current speed mode
                ecu.CurrentCommunicationSpeedMode = speedMode;

                _logger?.LogInformation($"Communication speed mode set to {speedMode} for ECU {ecu.Name} via CAN", "CANProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to set communication speed mode to {speedMode} for ECU {ecu?.Name} via CAN: {ex.Message}", "CANProtocolHandler");
                return false;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Validates the ECU
        /// </summary>
        /// <param name="ecu">The ECU to validate</param>
        /// <returns>True if the ECU is valid, false otherwise</returns>
        private new bool ValidateECU(ECUDevice? ecu)
        {
            if (ecu == null)
            {
                _logger?.LogError("ECU is null", "CANProtocolHandler");
                return false;
            }

            if (ecu.ProtocolType != ECUProtocolType.CAN)
            {
                _logger?.LogError($"ECU {ecu.Name} does not use CAN protocol", "CANProtocolHandler");
                return false;
            }

            return true;
        }

        #endregion
    }
}
