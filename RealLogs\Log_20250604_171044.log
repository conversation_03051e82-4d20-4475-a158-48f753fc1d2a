Log started at 6/4/2025 5:10:44 PM
2025-06-04 17:10:44.872 [Information] LoggingService: Logging service initialized
2025-06-04 17:10:44.886 [Information] AppConfigurationService: Initializing configuration service
2025-06-04 17:10:44.887 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Config
2025-06-04 17:10:44.888 [Information] AppConfigurationService: Configuration file not found, creating default
2025-06-04 17:10:44.931 [Warning] AppConfigurationService: Configuration service not initialized
2025-06-04 17:10:44.931 [Information] AppConfigurationService: Default configuration created
2025-06-04 17:10:44.932 [Information] AppConfigurationService: Configuration service initialized successfully
2025-06-04 17:10:44.933 [Information] App: Configuration service initialized successfully
2025-06-04 17:10:44.934 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-06-04 17:10:44.934 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: ''
2025-06-04 17:10:44.935 [Information] App: Environment variable exists: False, not 'false': True
2025-06-04 17:10:44.935 [Information] App: Final useDummyImplementations value: False
2025-06-04 17:10:44.935 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: ''
2025-06-04 17:10:44.936 [Information] App: usePatchedImplementation flag is: False
2025-06-04 17:10:44.936 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: ''
2025-06-04 17:10:44.936 [Information] App: APCI_LIBRARY_PATH environment variable is set to: ''
2025-06-04 17:10:44.937 [Information] App: VERBOSE_LOGGING environment variable is set to: ''
2025-06-04 17:10:44.937 [Information] App: verboseLogging flag is: False
2025-06-04 17:10:44.939 [Information] App: Verifying real hardware requirements...
2025-06-04 17:10:44.940 [Warning] App: ✗ Missing critical library: WUDFPuma.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\WUDFPuma.dll
2025-06-04 17:10:44.940 [Warning] App: ✗ Missing critical library: apci.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\apci.dll
2025-06-04 17:10:44.940 [Warning] App: ✗ Missing critical library: Volvo.ApciPlus.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\Volvo.ApciPlus.dll
2025-06-04 17:10:44.941 [Warning] App: ✗ Missing critical library: Volvo.ApciPlusData.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\Volvo.ApciPlusData.dll
2025-06-04 17:10:44.943 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 17:10:44.944 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-06-04 17:10:44.944 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Vocom\config.json
2025-06-04 17:10:44.945 [Warning] App: ⚠ Some real hardware requirements are missing - application may fall back to dummy mode
2025-06-04 17:10:44.957 [Information] App: Creating standard VocomServiceFactory instance
2025-06-04 17:10:44.958 [Information] App: Successfully created standard VocomServiceFactory instance
2025-06-04 17:10:44.959 [Information] App: Using VolvoFlashWR.Communication.Vocom.VocomServiceFactory Vocom service factory
2025-06-04 17:10:44.959 [Information] App: Checking if PTT application is running before creating Vocom service
2025-06-04 17:10:45.021 [Information] App: Creating Vocom service (attempt 1/3)
2025-06-04 17:10:45.025 [Information] VocomServiceFactory: Creating Vocom service with default settings
2025-06-04 17:10:45.025 [Information] VocomServiceFactory: Phoenix Vocom adapter not enabled, skipping
2025-06-04 17:10:45.026 [Information] VocomServiceFactory: Phoenix adapter initialization failed, attempting to create standard Vocom driver
2025-06-04 17:10:45.028 [Information] VocomDriver: Initializing Vocom driver
2025-06-04 17:10:45.030 [Information] VocomNativeInterop: Initializing Vocom driver
2025-06-04 17:10:45.037 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-06-04 17:10:45.037 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 17:10:45.038 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 17:10:45.039 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 17:10:45.041 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-06-04 17:10:45.072 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-06-04 17:10:45.091 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-06-04 17:10:45.093 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-06-04 17:10:45.097 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-06-04 17:10:45.097 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-06-04 17:10:45.098 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-06-04 17:10:45.104 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-06-04 17:10:45.108 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-06-04 17:10:45.112 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-06-04 17:10:45.114 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-06-04 17:10:45.114 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-06-04 17:10:45.114 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-06-04 17:10:45.115 [Information] VocomDriver: Vocom driver initialized successfully
2025-06-04 17:10:45.120 [Information] VocomService: Initializing Vocom service with dependencies
2025-06-04 17:10:45.121 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-06-04 17:10:45.123 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-06-04 17:10:45.125 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-06-04 17:10:45.236 [Information] WiFiCommunicationService: WiFi is available
2025-06-04 17:10:45.237 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-06-04 17:10:45.239 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-06-04 17:10:45.241 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-06-04 17:10:45.243 [Information] BluetoothCommunicationService: Bluetooth is available
2025-06-04 17:10:45.244 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-06-04 17:10:45.246 [Information] VocomService: Initializing Vocom service
2025-06-04 17:10:45.250 [Information] VocomService: Checking if PTT application is running
2025-06-04 17:10:45.270 [Information] VocomService: PTT application is not running
2025-06-04 17:10:45.273 [Information] VocomService: Vocom service initialized successfully
2025-06-04 17:10:45.275 [Information] VocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-06-04 17:10:45.275 [Information] App: Initializing Vocom service
2025-06-04 17:10:45.276 [Information] VocomService: Initializing Vocom service
2025-06-04 17:10:45.276 [Information] VocomService: Checking if PTT application is running
2025-06-04 17:10:45.294 [Information] VocomService: PTT application is not running
2025-06-04 17:10:45.295 [Information] VocomService: Vocom service initialized successfully
2025-06-04 17:10:45.300 [Information] VocomService: Scanning for Vocom devices
2025-06-04 17:10:45.315 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 17:10:45.347 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 17:10:45.352 [Information] VocomService: Found 2 Vocom devices
2025-06-04 17:10:45.353 [Information] App: Found 2 Vocom devices, attempting to connect to the first one
2025-06-04 17:10:45.356 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 17:10:45.357 [Information] VocomService: Checking if PTT application is running
2025-06-04 17:10:45.373 [Information] VocomService: PTT application is not running
2025-06-04 17:10:45.375 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 17:10:45.377 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-06-04 17:10:46.181 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 17:10:46.183 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 17:10:46.184 [Information] App: Connected to Vocom device Vocom - 88890300 (Bluetooth)
2025-06-04 17:10:46.188 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-06-04 17:10:46.192 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:10:46.193 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-06-04 17:10:46.197 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 17:10:46.199 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 17:10:46.200 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 17:10:46.204 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 17:10:46.206 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 17:10:46.237 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 17:10:46.240 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 17:10:46.258 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 17:10:46.266 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 17:10:46.269 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 17:10:46.281 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 17:10:46.282 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 17:10:46.283 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 17:10:46.283 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 17:10:46.283 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 17:10:46.284 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 17:10:46.284 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 17:10:46.284 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 17:10:46.285 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 17:10:46.290 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 17:10:46.291 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 17:10:46.291 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 17:10:46.291 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 17:10:46.292 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 17:10:46.292 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 17:10:46.292 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 17:10:46.293 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 17:10:46.297 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 17:10:46.301 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.301 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.301 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.302 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.303 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.305 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.308 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.308 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.310 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.311 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-04 17:10:46.313 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-04 17:10:46.314 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 17:10:46.319 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 17:10:46.321 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.322 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.322 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.322 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.323 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.323 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.323 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.324 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.324 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.324 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.327 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.328 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.335 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.335 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.336 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.336 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.336 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.336 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.337 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.337 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.337 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.338 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.338 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.338 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.344 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.344 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.345 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.345 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.345 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.346 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.346 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.346 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.347 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.347 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.347 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.348 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.354 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.354 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.355 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.355 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.355 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.355 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.356 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.356 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.356 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.357 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.357 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.358 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.364 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.364 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.365 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.365 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.366 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.366 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.366 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.367 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.367 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.367 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.368 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.368 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.374 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.374 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.374 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.375 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.375 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.375 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.376 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.376 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.376 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.377 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.377 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.377 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.383 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.383 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.383 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.384 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.384 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.384 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.385 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.385 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.385 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.386 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.386 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.386 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.393 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.393 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.393 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.394 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.394 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.394 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.395 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.395 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.395 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.396 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.396 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.396 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.402 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.403 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.403 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.403 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.404 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.404 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.404 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.405 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.405 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.406 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.406 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.406 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.413 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.413 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.414 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.414 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.414 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.414 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.415 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.416 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.416 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.416 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.417 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.417 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.423 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.423 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.423 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.424 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.424 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.424 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.425 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.425 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.425 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.426 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.426 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.426 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.431 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.431 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.432 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.433 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.433 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.433 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.434 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.434 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.434 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.435 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.435 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.435 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.441 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.441 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.441 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.442 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.442 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.442 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.443 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.443 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.443 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.443 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.444 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.444 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.450 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.450 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.450 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.451 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.451 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.451 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.452 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.452 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.452 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.453 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.453 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.453 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.459 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.459 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.460 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.460 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.460 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.460 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.461 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.461 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.462 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.462 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.462 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.462 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.469 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.469 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.470 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.470 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.470 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.471 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.471 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.471 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.471 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.472 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.472 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.472 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.478 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.478 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.478 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.479 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.479 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.480 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.480 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.480 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.481 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.481 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.481 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.482 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.488 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.488 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.488 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.489 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.489 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.489 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.490 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.490 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.490 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.490 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.491 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.491 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.497 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.497 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.498 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.498 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.498 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.499 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.499 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.499 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.499 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.500 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.500 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.500 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.507 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:46.507 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.507 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.508 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.508 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:46.508 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.508 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.509 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.509 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.509 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.510 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.510 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:46.516 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 17:10:46.517 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-04 17:10:46.520 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 17:10:46.520 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 17:10:46.532 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 17:10:46.533 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 17:10:46.533 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 17:10:46.536 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:46.537 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:46.537 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:46.537 [Information] VocomService: Using generic data transfer
2025-06-04 17:10:46.539 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 17:10:46.540 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 17:10:46.540 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.541 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:46.541 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:46.543 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 17:10:46.543 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:46.545 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 17:10:46.545 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 17:10:46.547 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 17:10:46.548 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 17:10:46.559 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 17:10:46.560 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 17:10:46.560 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 17:10:46.571 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 17:10:46.582 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 17:10:46.593 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 17:10:46.604 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 17:10:46.615 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 17:10:46.617 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 17:10:46.618 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 17:10:46.629 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 17:10:46.630 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 17:10:46.631 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 17:10:46.642 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 17:10:46.653 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 17:10:46.664 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 17:10:46.675 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 17:10:46.686 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 17:10:46.697 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 17:10:46.699 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 17:10:46.699 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 17:10:46.711 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 17:10:46.712 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 17:10:46.712 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 17:10:46.713 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 17:10:46.713 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 17:10:46.713 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 17:10:46.714 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 17:10:46.714 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 17:10:46.714 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 17:10:46.714 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 17:10:46.715 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 17:10:46.715 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 17:10:46.715 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 17:10:46.716 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 17:10:46.716 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 17:10:46.716 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 17:10:46.717 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 17:10:46.818 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 17:10:46.818 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 17:10:46.822 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 17:10:46.824 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:10:46.824 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 17:10:46.825 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 17:10:46.825 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:10:46.826 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 17:10:46.826 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 17:10:46.826 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:10:46.827 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 17:10:46.827 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 17:10:46.827 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:10:46.828 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 17:10:46.828 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 17:10:46.829 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-06-04 17:10:46.834 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-06-04 17:10:46.835 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-06-04 17:10:46.885 [Information] BackupService: Initializing backup service
2025-06-04 17:10:46.886 [Information] BackupService: Backup service initialized successfully
2025-06-04 17:10:46.886 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-06-04 17:10:46.886 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-06-04 17:10:46.889 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-06-04 17:10:46.988 [Information] BackupService: Compressing backup data
2025-06-04 17:10:47.030 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (449 bytes)
2025-06-04 17:10:47.031 [Information] BackupServiceFactory: Created template for category: Production
2025-06-04 17:10:47.032 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-06-04 17:10:47.034 [Information] BackupService: Compressing backup data
2025-06-04 17:10:47.042 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-06-04 17:10:47.043 [Information] BackupServiceFactory: Created template for category: Development
2025-06-04 17:10:47.044 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-06-04 17:10:47.044 [Information] BackupService: Compressing backup data
2025-06-04 17:10:47.046 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (448 bytes)
2025-06-04 17:10:47.046 [Information] BackupServiceFactory: Created template for category: Testing
2025-06-04 17:10:47.047 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-06-04 17:10:47.047 [Information] BackupService: Compressing backup data
2025-06-04 17:10:47.049 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (449 bytes)
2025-06-04 17:10:47.049 [Information] BackupServiceFactory: Created template for category: Archived
2025-06-04 17:10:47.050 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-06-04 17:10:47.050 [Information] BackupService: Compressing backup data
2025-06-04 17:10:47.051 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (448 bytes)
2025-06-04 17:10:47.052 [Information] BackupServiceFactory: Created template for category: Critical
2025-06-04 17:10:47.052 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-06-04 17:10:47.053 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-06-04 17:10:47.053 [Information] BackupService: Compressing backup data
2025-06-04 17:10:47.055 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (515 bytes)
2025-06-04 17:10:47.055 [Information] BackupServiceFactory: Created template with predefined tags
2025-06-04 17:10:47.055 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-06-04 17:10:47.060 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-06-04 17:10:47.068 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 17:10:47.070 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 17:10:47.150 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 17:10:47.151 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 17:10:47.153 [Information] BackupSchedulerService: Starting backup scheduler
2025-06-04 17:10:47.153 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-06-04 17:10:47.153 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-06-04 17:10:47.155 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-06-04 17:10:47.156 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-06-04 17:10:47.160 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-06-04 17:10:47.160 [Information] App: Flash operation monitor service initialized successfully
2025-06-04 17:10:47.171 [Information] LicensingService: Initializing licensing service
2025-06-04 17:10:47.219 [Information] LicensingService: License information loaded successfully
2025-06-04 17:10:47.221 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-06-04 17:10:47.222 [Information] App: Licensing service initialized successfully
2025-06-04 17:10:47.222 [Information] App: License status: Trial
2025-06-04 17:10:47.222 [Information] App: Trial period: 30 days remaining
2025-06-04 17:10:47.223 [Information] BackupSchedulerService: Getting all backup schedules
2025-06-04 17:10:47.417 [Information] VocomService: Initializing Vocom service
2025-06-04 17:10:47.417 [Information] VocomService: Checking if PTT application is running
2025-06-04 17:10:47.431 [Information] VocomService: PTT application is not running
2025-06-04 17:10:47.432 [Information] VocomService: Vocom service initialized successfully
2025-06-04 17:10:47.483 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 17:10:47.483 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 17:10:47.483 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 17:10:47.484 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 17:10:47.484 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 17:10:47.485 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 17:10:47.486 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 17:10:47.487 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 17:10:47.488 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 17:10:47.488 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 17:10:47.499 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 17:10:47.499 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 17:10:47.500 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 17:10:47.500 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 17:10:47.500 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 17:10:47.500 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 17:10:47.501 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 17:10:47.501 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 17:10:47.501 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 17:10:47.501 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 17:10:47.502 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 17:10:47.502 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 17:10:47.502 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 17:10:47.503 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 17:10:47.503 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 17:10:47.503 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 17:10:47.503 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 17:10:47.504 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 17:10:47.504 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.504 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.505 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.505 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.505 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.505 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.506 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.506 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.507 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.507 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-04 17:10:47.508 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-04 17:10:47.508 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 17:10:47.508 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 17:10:47.509 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.509 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.509 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.509 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.510 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.510 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.510 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.511 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.511 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.513 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.513 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.513 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.520 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.520 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.520 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.521 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.521 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.521 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.521 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.522 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.522 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.523 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.523 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.523 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.530 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.530 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.531 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.531 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.531 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.531 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.532 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.532 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.533 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.534 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.535 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.535 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.542 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.542 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.542 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.543 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.543 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.543 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.543 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.544 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.544 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.545 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.545 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.545 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.551 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.551 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.551 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.552 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.552 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.552 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.552 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.553 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.553 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.554 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.554 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.554 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.561 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.561 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.561 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.562 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.562 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.562 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.563 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.563 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.563 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.564 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.565 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.566 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.572 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.572 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.572 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.573 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.573 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.573 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.573 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.574 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.574 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.574 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.575 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.575 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.582 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.582 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.582 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.583 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.583 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.583 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.584 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.584 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.585 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.586 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.586 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.586 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.593 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.593 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.593 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.594 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.594 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.594 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.594 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.595 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.596 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.596 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.597 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.597 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.603 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.603 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.603 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.604 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.604 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.604 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.604 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.605 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.605 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.606 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.606 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.606 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.613 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.613 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.613 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.614 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.614 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.614 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.615 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.615 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.616 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.617 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.618 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.618 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.625 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.625 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.625 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.626 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.626 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.626 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.626 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.627 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.627 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.627 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.628 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.628 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.635 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.635 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.635 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.636 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.636 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.636 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.637 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.637 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.637 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.638 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.638 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.639 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.645 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.645 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.645 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.646 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.646 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.646 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.646 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.647 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.647 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.648 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.648 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.649 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.655 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.655 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.655 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.656 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.656 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.656 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.656 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.657 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.657 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.658 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.658 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.658 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.665 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.665 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.666 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.666 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.666 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.666 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.667 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.668 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.669 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.670 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.670 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.671 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.677 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.677 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.677 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.678 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.678 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.678 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.678 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.679 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.679 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.679 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.680 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.680 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.687 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.687 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.687 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.688 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.688 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.688 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.688 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.689 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.689 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.689 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.690 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.690 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.697 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.697 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.697 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.698 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.698 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.698 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.698 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.700 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.700 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.701 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.701 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.702 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.708 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:10:47.708 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.708 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.708 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.709 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:10:47.709 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.709 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.710 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.710 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.710 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.711 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.711 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:10:47.717 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 17:10:47.717 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-04 17:10:47.717 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 17:10:47.718 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 17:10:47.729 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 17:10:47.729 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 17:10:47.729 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 17:10:47.730 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:10:47.730 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:10:47.730 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:10:47.731 [Information] VocomService: Using generic data transfer
2025-06-04 17:10:47.731 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 17:10:47.731 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 17:10:47.731 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.734 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:10:47.734 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:10:47.735 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 17:10:47.735 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:10:47.736 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 17:10:47.736 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 17:10:47.737 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 17:10:47.737 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 17:10:47.748 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 17:10:47.748 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 17:10:47.749 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 17:10:47.760 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 17:10:47.771 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 17:10:47.782 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 17:10:47.793 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 17:10:47.804 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 17:10:47.804 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 17:10:47.804 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 17:10:47.815 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 17:10:47.816 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 17:10:47.816 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 17:10:47.827 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 17:10:47.839 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 17:10:47.850 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 17:10:47.860 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 17:10:47.871 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 17:10:47.882 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 17:10:47.883 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 17:10:47.883 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 17:10:47.895 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 17:10:47.895 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 17:10:47.895 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 17:10:47.896 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 17:10:47.896 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 17:10:47.896 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 17:10:47.897 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 17:10:47.897 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 17:10:47.897 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 17:10:47.898 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 17:10:47.898 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 17:10:47.898 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 17:10:47.898 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 17:10:47.899 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 17:10:47.899 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 17:10:47.899 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 17:10:47.900 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 17:10:48.000 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 17:10:48.001 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 17:10:48.001 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 17:10:48.002 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:10:48.002 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 17:10:48.003 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 17:10:48.003 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:10:48.003 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 17:10:48.004 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 17:10:48.004 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:10:48.004 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 17:10:48.004 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 17:10:48.005 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:10:48.005 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 17:10:48.005 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 17:10:48.056 [Information] BackupService: Initializing backup service
2025-06-04 17:10:48.057 [Information] BackupService: Backup service initialized successfully
2025-06-04 17:10:48.108 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 17:10:48.109 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 17:10:48.111 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 17:10:48.111 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 17:10:48.163 [Information] BackupService: Getting predefined backup categories
2025-06-04 17:10:48.216 [Information] MainViewModel: Services initialized successfully
2025-06-04 17:10:48.218 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 17:10:48.220 [Information] VocomService: Scanning for Vocom devices
2025-06-04 17:10:48.221 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 17:10:48.221 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 17:10:48.224 [Information] VocomService: Found 2 Vocom devices
2025-06-04 17:10:48.225 [Information] MainViewModel: Found 2 Vocom device(s)
