Log started at 6/4/2025 5:11:26 PM
2025-06-04 17:11:26.765 [Information] LoggingService: Logging service initialized
2025-06-04 17:11:26.781 [Information] AppConfigurationService: Initializing configuration service
2025-06-04 17:11:26.782 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Config
2025-06-04 17:11:26.782 [Information] AppConfigurationService: Configuration file not found, creating default
2025-06-04 17:11:26.787 [Warning] AppConfigurationService: Configuration service not initialized
2025-06-04 17:11:26.788 [Information] AppConfigurationService: Default configuration created
2025-06-04 17:11:26.789 [Information] AppConfigurationService: Configuration service initialized successfully
2025-06-04 17:11:26.789 [Information] App: Configuration service initialized successfully
2025-06-04 17:11:26.790 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-06-04 17:11:26.790 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: ''
2025-06-04 17:11:26.791 [Information] App: Environment variable exists: False, not 'false': True
2025-06-04 17:11:26.791 [Information] App: Final useDummyImplementations value: False
2025-06-04 17:11:26.791 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: ''
2025-06-04 17:11:26.792 [Information] App: usePatchedImplementation flag is: False
2025-06-04 17:11:26.792 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: ''
2025-06-04 17:11:26.792 [Information] App: APCI_LIBRARY_PATH environment variable is set to: ''
2025-06-04 17:11:26.792 [Information] App: VERBOSE_LOGGING environment variable is set to: ''
2025-06-04 17:11:26.793 [Information] App: verboseLogging flag is: False
2025-06-04 17:11:26.795 [Information] App: Verifying real hardware requirements...
2025-06-04 17:11:26.795 [Warning] App: ✗ Missing critical library: WUDFPuma.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\WUDFPuma.dll
2025-06-04 17:11:26.796 [Warning] App: ✗ Missing critical library: apci.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\apci.dll
2025-06-04 17:11:26.796 [Warning] App: ✗ Missing critical library: Volvo.ApciPlus.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\Volvo.ApciPlus.dll
2025-06-04 17:11:26.797 [Warning] App: ✗ Missing critical library: Volvo.ApciPlusData.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\Volvo.ApciPlusData.dll
2025-06-04 17:11:26.797 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 17:11:26.797 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-06-04 17:11:26.798 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Vocom\config.json
2025-06-04 17:11:26.798 [Warning] App: ⚠ Some real hardware requirements are missing - application may fall back to dummy mode
2025-06-04 17:11:26.810 [Information] App: Creating standard VocomServiceFactory instance
2025-06-04 17:11:26.811 [Information] App: Successfully created standard VocomServiceFactory instance
2025-06-04 17:11:26.811 [Information] App: Using VolvoFlashWR.Communication.Vocom.VocomServiceFactory Vocom service factory
2025-06-04 17:11:26.812 [Information] App: Checking if PTT application is running before creating Vocom service
2025-06-04 17:11:26.857 [Information] App: Creating Vocom service (attempt 1/3)
2025-06-04 17:11:26.860 [Information] VocomServiceFactory: Creating Vocom service with default settings
2025-06-04 17:11:26.860 [Information] VocomServiceFactory: Phoenix Vocom adapter not enabled, skipping
2025-06-04 17:11:26.861 [Information] VocomServiceFactory: Phoenix adapter initialization failed, attempting to create standard Vocom driver
2025-06-04 17:11:26.862 [Information] VocomDriver: Initializing Vocom driver
2025-06-04 17:11:26.864 [Information] VocomNativeInterop: Initializing Vocom driver
2025-06-04 17:11:26.870 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-06-04 17:11:26.870 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 17:11:26.871 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 17:11:26.872 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 17:11:26.873 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-06-04 17:11:26.877 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-06-04 17:11:26.878 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-06-04 17:11:26.881 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-06-04 17:11:26.882 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-06-04 17:11:26.883 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-06-04 17:11:26.884 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-06-04 17:11:26.888 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-06-04 17:11:26.889 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-06-04 17:11:26.891 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-06-04 17:11:26.892 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-06-04 17:11:26.892 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-06-04 17:11:26.892 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-06-04 17:11:26.893 [Information] VocomDriver: Vocom driver initialized successfully
2025-06-04 17:11:26.897 [Information] VocomService: Initializing Vocom service with dependencies
2025-06-04 17:11:26.899 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-06-04 17:11:26.900 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-06-04 17:11:26.902 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-06-04 17:11:26.985 [Information] WiFiCommunicationService: WiFi is available
2025-06-04 17:11:26.986 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-06-04 17:11:26.988 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-06-04 17:11:26.990 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-06-04 17:11:26.993 [Information] BluetoothCommunicationService: Bluetooth is available
2025-06-04 17:11:26.994 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-06-04 17:11:26.996 [Information] VocomService: Initializing Vocom service
2025-06-04 17:11:26.999 [Information] VocomService: Checking if PTT application is running
2025-06-04 17:11:27.013 [Information] VocomService: PTT application is not running
2025-06-04 17:11:27.017 [Information] VocomService: Vocom service initialized successfully
2025-06-04 17:11:27.018 [Information] VocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-06-04 17:11:27.018 [Information] App: Initializing Vocom service
2025-06-04 17:11:27.019 [Information] VocomService: Initializing Vocom service
2025-06-04 17:11:27.019 [Information] VocomService: Checking if PTT application is running
2025-06-04 17:11:27.035 [Information] VocomService: PTT application is not running
2025-06-04 17:11:27.035 [Information] VocomService: Vocom service initialized successfully
2025-06-04 17:11:27.039 [Information] VocomService: Scanning for Vocom devices
2025-06-04 17:11:27.044 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 17:11:27.073 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 17:11:27.078 [Information] VocomService: Found 2 Vocom devices
2025-06-04 17:11:27.078 [Information] App: Found 2 Vocom devices, attempting to connect to the first one
2025-06-04 17:11:27.082 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 17:11:27.083 [Information] VocomService: Checking if PTT application is running
2025-06-04 17:11:27.097 [Information] VocomService: PTT application is not running
2025-06-04 17:11:27.099 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 17:11:27.101 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-06-04 17:11:27.907 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 17:11:27.908 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 17:11:27.909 [Information] App: Connected to Vocom device Vocom - 88890300 (Bluetooth)
2025-06-04 17:11:27.913 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-06-04 17:11:27.917 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:27.918 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-06-04 17:11:27.923 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 17:11:27.925 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 17:11:27.926 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 17:11:27.931 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 17:11:27.933 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 17:11:27.945 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 17:11:27.947 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 17:11:27.950 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 17:11:27.956 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 17:11:27.959 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 17:11:27.970 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 17:11:27.971 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 17:11:27.972 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 17:11:27.972 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 17:11:27.973 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 17:11:27.973 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 17:11:27.973 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 17:11:27.974 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 17:11:27.974 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 17:11:27.979 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 17:11:27.980 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 17:11:27.980 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 17:11:27.981 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 17:11:27.981 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 17:11:27.981 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 17:11:27.982 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 17:11:27.982 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 17:11:27.986 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 17:11:27.991 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:27.991 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:27.992 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.001 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.002 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.005 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.007 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.008 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.010 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.011 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-04 17:11:28.012 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-04 17:11:28.014 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 17:11:28.018 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 17:11:28.021 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.021 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.022 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.022 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.022 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.022 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.023 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.023 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.023 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.024 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.024 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.025 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.032 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.033 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.033 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.034 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.034 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.034 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.035 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.035 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.035 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.036 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.036 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.036 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.042 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.043 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.043 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.043 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.044 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.044 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.044 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.045 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.045 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.045 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.046 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.046 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.052 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.053 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.053 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.053 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.054 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.054 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.054 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.055 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.055 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.055 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.055 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.056 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.061 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.062 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.062 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.062 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.063 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.063 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.063 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.064 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.064 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.065 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.065 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.065 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.071 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.072 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.072 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.072 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.073 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.073 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.073 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.074 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.074 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.074 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.075 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.075 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.080 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.081 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.081 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.081 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.082 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.082 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.082 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.083 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.083 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.083 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.084 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.084 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.089 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.090 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.090 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.090 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.091 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.091 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.091 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.092 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.092 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.092 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.092 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.093 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.098 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.099 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.099 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.099 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.100 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.100 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.100 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.101 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.101 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.101 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.102 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.102 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.107 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.108 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.108 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.108 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.109 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.109 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.109 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.110 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.110 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.110 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.110 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.111 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.116 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.117 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.117 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.117 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.118 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.118 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.118 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.118 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.119 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.119 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.119 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.120 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.125 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.126 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.126 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.126 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.127 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.127 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.127 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.128 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.128 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.128 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.129 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.129 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.135 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.136 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.136 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.136 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.137 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.137 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.137 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.138 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.138 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.138 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.138 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.139 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.144 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.145 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.145 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.145 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.146 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.146 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.147 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.147 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.147 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.148 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.148 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.148 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.154 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.155 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.155 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.155 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.156 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.156 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.156 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.157 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.157 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.157 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.157 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.158 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.163 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.164 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.164 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.164 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.165 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.165 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.165 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.166 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.166 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.166 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.167 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.167 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.173 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.174 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.174 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.174 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.175 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.175 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.175 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.176 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.176 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.176 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.176 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.177 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.182 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.183 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.183 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.183 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.184 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.184 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.184 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.185 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.185 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.185 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.186 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.186 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.191 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.192 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.192 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.192 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.193 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.193 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.193 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.194 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.194 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.194 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.194 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.195 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.200 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:28.201 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.201 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.201 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.202 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:28.202 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.202 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.203 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.203 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.203 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.203 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.204 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:28.209 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 17:11:28.210 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-04 17:11:28.213 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 17:11:28.214 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 17:11:28.224 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 17:11:28.225 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 17:11:28.226 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 17:11:28.228 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:28.228 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:28.229 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:28.229 [Information] VocomService: Using generic data transfer
2025-06-04 17:11:28.232 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 17:11:28.233 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 17:11:28.234 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.234 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:28.234 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:28.236 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 17:11:28.236 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:28.238 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 17:11:28.238 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 17:11:28.240 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 17:11:28.241 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 17:11:28.252 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 17:11:28.253 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 17:11:28.254 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 17:11:28.264 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 17:11:28.275 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 17:11:28.286 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 17:11:28.297 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 17:11:28.308 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 17:11:28.310 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 17:11:28.311 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 17:11:28.321 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 17:11:28.322 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 17:11:28.322 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 17:11:28.333 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 17:11:28.344 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 17:11:28.355 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 17:11:28.366 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 17:11:28.377 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 17:11:28.388 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 17:11:28.390 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 17:11:28.391 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 17:11:28.402 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 17:11:28.403 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 17:11:28.404 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 17:11:28.404 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 17:11:28.404 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 17:11:28.405 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 17:11:28.405 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 17:11:28.405 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 17:11:28.406 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 17:11:28.406 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 17:11:28.406 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 17:11:28.407 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 17:11:28.407 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 17:11:28.407 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 17:11:28.407 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 17:11:28.408 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 17:11:28.408 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 17:11:28.508 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 17:11:28.509 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 17:11:28.512 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 17:11:28.514 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:28.515 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 17:11:28.515 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 17:11:28.515 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:28.516 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 17:11:28.516 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 17:11:28.517 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:28.517 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 17:11:28.517 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 17:11:28.518 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:28.518 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 17:11:28.519 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 17:11:28.520 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-06-04 17:11:28.524 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-06-04 17:11:28.526 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-06-04 17:11:28.532 [Information] BackupService: Initializing backup service
2025-06-04 17:11:28.532 [Information] BackupService: Backup service initialized successfully
2025-06-04 17:11:28.532 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-06-04 17:11:28.533 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-06-04 17:11:28.536 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-06-04 17:11:28.622 [Information] BackupService: Compressing backup data
2025-06-04 17:11:28.630 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (450 bytes)
2025-06-04 17:11:28.632 [Information] BackupServiceFactory: Created template for category: Production
2025-06-04 17:11:28.632 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-06-04 17:11:28.633 [Information] BackupService: Compressing backup data
2025-06-04 17:11:28.642 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (449 bytes)
2025-06-04 17:11:28.644 [Information] BackupServiceFactory: Created template for category: Development
2025-06-04 17:11:28.645 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-06-04 17:11:28.645 [Information] BackupService: Compressing backup data
2025-06-04 17:11:28.647 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (447 bytes)
2025-06-04 17:11:28.647 [Information] BackupServiceFactory: Created template for category: Testing
2025-06-04 17:11:28.649 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-06-04 17:11:28.650 [Information] BackupService: Compressing backup data
2025-06-04 17:11:28.652 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (452 bytes)
2025-06-04 17:11:28.652 [Information] BackupServiceFactory: Created template for category: Archived
2025-06-04 17:11:28.653 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-06-04 17:11:28.653 [Information] BackupService: Compressing backup data
2025-06-04 17:11:28.654 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (452 bytes)
2025-06-04 17:11:28.655 [Information] BackupServiceFactory: Created template for category: Critical
2025-06-04 17:11:28.655 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-06-04 17:11:28.656 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-06-04 17:11:28.656 [Information] BackupService: Compressing backup data
2025-06-04 17:11:28.657 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-06-04 17:11:28.658 [Information] BackupServiceFactory: Created template with predefined tags
2025-06-04 17:11:28.658 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-06-04 17:11:28.660 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-06-04 17:11:28.664 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 17:11:28.666 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 17:11:28.737 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 17:11:28.738 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 17:11:28.739 [Information] BackupSchedulerService: Starting backup scheduler
2025-06-04 17:11:28.740 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-06-04 17:11:28.740 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-06-04 17:11:28.742 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-06-04 17:11:28.742 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-06-04 17:11:28.747 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-06-04 17:11:28.748 [Information] App: Flash operation monitor service initialized successfully
2025-06-04 17:11:28.757 [Information] LicensingService: Initializing licensing service
2025-06-04 17:11:28.814 [Information] LicensingService: License information loaded successfully
2025-06-04 17:11:28.817 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-06-04 17:11:28.818 [Information] App: Licensing service initialized successfully
2025-06-04 17:11:28.818 [Information] App: License status: Trial
2025-06-04 17:11:28.818 [Information] App: Trial period: 30 days remaining
2025-06-04 17:11:28.819 [Information] BackupSchedulerService: Getting all backup schedules
2025-06-04 17:11:29.029 [Information] VocomService: Initializing Vocom service
2025-06-04 17:11:29.029 [Information] VocomService: Checking if PTT application is running
2025-06-04 17:11:29.043 [Information] VocomService: PTT application is not running
2025-06-04 17:11:29.044 [Information] VocomService: Vocom service initialized successfully
2025-06-04 17:11:29.094 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 17:11:29.094 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 17:11:29.094 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 17:11:29.095 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 17:11:29.095 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 17:11:29.097 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 17:11:29.098 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 17:11:29.099 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 17:11:29.100 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 17:11:29.100 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 17:11:29.111 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 17:11:29.111 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 17:11:29.112 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 17:11:29.112 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 17:11:29.112 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 17:11:29.113 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 17:11:29.113 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 17:11:29.113 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 17:11:29.113 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 17:11:29.114 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 17:11:29.114 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 17:11:29.114 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 17:11:29.115 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 17:11:29.115 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 17:11:29.115 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 17:11:29.115 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 17:11:29.116 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 17:11:29.116 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 17:11:29.116 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.117 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.117 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.117 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.117 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.118 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.118 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.118 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.119 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.119 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-04 17:11:29.120 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-04 17:11:29.120 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 17:11:29.120 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 17:11:29.121 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.121 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.121 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.121 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.122 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.122 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.122 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.123 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.123 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.123 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.124 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.124 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.131 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.131 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.132 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.132 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.132 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.132 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.133 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.133 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.133 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.134 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.134 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.135 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.141 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.141 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.141 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.142 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.142 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.142 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.142 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.143 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.143 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.144 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.144 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.144 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.151 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.151 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.151 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.152 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.152 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.152 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.152 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.153 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.153 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.153 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.154 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.154 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.161 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.161 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.161 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.162 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.162 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.162 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.163 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.163 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.163 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.164 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.165 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.165 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.171 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.171 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.171 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.172 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.172 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.172 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.172 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.173 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.173 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.174 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.174 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.174 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.181 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.181 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.181 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.182 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.182 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.182 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.182 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.183 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.183 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.183 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.184 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.184 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.191 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.191 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.191 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.192 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.192 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.192 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.193 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.193 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.193 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.194 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.194 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.194 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.201 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.201 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.201 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.202 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.202 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.202 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.202 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.203 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.203 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.203 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.204 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.204 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.211 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.211 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.211 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.212 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.212 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.212 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.213 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.213 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.213 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.214 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.214 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.215 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.221 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.221 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.221 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.222 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.222 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.222 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.222 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.223 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.223 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.224 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.224 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.224 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.231 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.231 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.232 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.232 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.232 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.232 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.233 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.233 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.233 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.234 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.234 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.235 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.241 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.241 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.241 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.242 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.242 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.242 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.242 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.243 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.243 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.243 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.244 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.244 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.251 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.251 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.251 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.252 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.252 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.252 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.252 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.253 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.253 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.253 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.254 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.254 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.261 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.261 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.262 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.262 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.262 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.262 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.263 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.263 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.264 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.265 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.265 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.265 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.272 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.272 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.272 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.273 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.273 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.273 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.273 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.274 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.274 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.274 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.275 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.275 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.281 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.282 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.282 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.282 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.283 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.283 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.283 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.284 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.284 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.284 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.285 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.285 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.292 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.292 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.292 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.293 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.293 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.293 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.294 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.294 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.294 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.295 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.295 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.296 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.302 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.302 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.302 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.303 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.303 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.303 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.303 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.304 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.304 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.304 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.305 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.305 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.312 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 17:11:29.312 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.312 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.313 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.313 [Information] VocomService: Detected CAN protocol request
2025-06-04 17:11:29.313 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.314 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.336 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.338 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.338 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.339 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.339 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 17:11:29.346 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 17:11:29.346 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-04 17:11:29.347 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 17:11:29.347 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 17:11:29.359 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 17:11:29.359 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 17:11:29.359 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 17:11:29.360 [Information] VocomService: Sending data and waiting for response
2025-06-04 17:11:29.360 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 17:11:29.360 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 17:11:29.361 [Information] VocomService: Using generic data transfer
2025-06-04 17:11:29.361 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 17:11:29.361 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 17:11:29.362 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.362 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 17:11:29.362 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 17:11:29.363 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 17:11:29.364 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 17:11:29.364 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 17:11:29.365 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 17:11:29.365 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 17:11:29.365 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 17:11:29.376 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 17:11:29.377 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 17:11:29.377 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 17:11:29.388 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 17:11:29.400 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 17:11:29.410 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 17:11:29.421 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 17:11:29.432 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 17:11:29.433 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 17:11:29.433 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 17:11:29.444 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 17:11:29.445 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 17:11:29.446 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 17:11:29.456 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 17:11:29.467 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 17:11:29.478 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 17:11:29.489 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 17:11:29.500 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 17:11:29.511 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 17:11:29.512 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 17:11:29.512 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 17:11:29.523 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 17:11:29.524 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 17:11:29.524 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 17:11:29.525 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 17:11:29.525 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 17:11:29.525 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 17:11:29.525 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 17:11:29.526 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 17:11:29.526 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 17:11:29.526 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 17:11:29.527 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 17:11:29.527 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 17:11:29.527 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 17:11:29.528 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 17:11:29.528 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 17:11:29.528 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 17:11:29.528 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 17:11:29.629 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 17:11:29.629 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 17:11:29.630 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 17:11:29.630 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:29.631 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 17:11:29.631 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 17:11:29.631 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:29.632 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 17:11:29.632 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 17:11:29.632 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:29.633 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 17:11:29.633 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 17:11:29.633 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 17:11:29.633 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 17:11:29.634 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 17:11:29.685 [Information] BackupService: Initializing backup service
2025-06-04 17:11:29.686 [Information] BackupService: Backup service initialized successfully
2025-06-04 17:11:29.737 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 17:11:29.737 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 17:11:29.738 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 17:11:29.739 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 17:11:29.791 [Information] BackupService: Getting predefined backup categories
2025-06-04 17:11:29.842 [Information] MainViewModel: Services initialized successfully
2025-06-04 17:11:29.845 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 17:11:29.846 [Information] VocomService: Scanning for Vocom devices
2025-06-04 17:11:29.847 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 17:11:29.848 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 17:11:29.850 [Information] VocomService: Found 2 Vocom devices
2025-06-04 17:11:29.851 [Information] MainViewModel: Found 2 Vocom device(s)
2025-06-04 17:11:52.672 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 17:11:52.674 [Information] VocomService: Scanning for Vocom devices
2025-06-04 17:11:52.674 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 17:11:52.675 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 17:11:52.677 [Information] VocomService: Found 2 Vocom devices
2025-06-04 17:11:52.678 [Information] MainViewModel: Found 2 Vocom device(s)
2025-06-04 07:35:10.547 [Information] LoggingService: Logging service initialized
2025-06-04 07:35:10.555 [Information] AppConfigurationService: Initializing configuration service
2025-06-04 07:35:10.556 [Information] AppConfigurationService: Created configuration directory: C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (1)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Config
2025-06-04 07:35:10.557 [Information] AppConfigurationService: Configuration file not found, creating default
2025-06-04 07:35:10.565 [Warning] AppConfigurationService: Configuration service not initialized
2025-06-04 07:35:10.566 [Information] AppConfigurationService: Default configuration created
2025-06-04 07:35:10.566 [Information] AppConfigurationService: Configuration service initialized successfully
2025-06-04 07:35:10.566 [Information] App: Configuration service initialized successfully
2025-06-04 07:35:10.567 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-06-04 07:35:10.567 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: ''
2025-06-04 07:35:10.568 [Information] App: Environment variable exists: False, not 'false': True
2025-06-04 07:35:10.568 [Information] App: Final useDummyImplementations value: False
2025-06-04 07:35:10.568 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: ''
2025-06-04 07:35:10.568 [Information] App: usePatchedImplementation flag is: False
2025-06-04 07:35:10.569 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: ''
2025-06-04 07:35:10.569 [Information] App: APCI_LIBRARY_PATH environment variable is set to: ''
2025-06-04 07:35:10.569 [Information] App: VERBOSE_LOGGING environment variable is set to: ''
2025-06-04 07:35:10.570 [Information] App: verboseLogging flag is: False
2025-06-04 07:35:10.571 [Information] App: Verifying real hardware requirements...
2025-06-04 07:35:10.573 [Warning] App: ✗ Missing critical library: WUDFPuma.dll at C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (1)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\WUDFPuma.dll
2025-06-04 07:35:10.574 [Warning] App: ✗ Missing critical library: apci.dll at C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (1)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\apci.dll
2025-06-04 07:35:10.574 [Warning] App: ✗ Missing critical library: Volvo.ApciPlus.dll at C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (1)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\Volvo.ApciPlus.dll
2025-06-04 07:35:10.575 [Warning] App: ✗ Missing critical library: Volvo.ApciPlusData.dll at C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (1)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\Volvo.ApciPlusData.dll
2025-06-04 07:35:10.575 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 07:35:10.576 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-06-04 07:35:10.576 [Information] App: ✓ Found Vocom driver config: C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (1)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Vocom\config.json
2025-06-04 07:35:10.577 [Warning] App: ⚠ Some real hardware requirements are missing - application may fall back to dummy mode
2025-06-04 07:35:10.590 [Information] App: Creating standard VocomServiceFactory instance
2025-06-04 07:35:10.590 [Information] App: Successfully created standard VocomServiceFactory instance
2025-06-04 07:35:10.591 [Information] App: Using VolvoFlashWR.Communication.Vocom.VocomServiceFactory Vocom service factory
2025-06-04 07:35:10.591 [Information] App: Checking if PTT application is running before creating Vocom service
2025-06-04 07:35:10.619 [Information] App: Creating Vocom service (attempt 1/3)
2025-06-04 07:35:10.621 [Information] VocomServiceFactory: Creating Vocom service with default settings
2025-06-04 07:35:10.622 [Information] VocomServiceFactory: Phoenix Vocom adapter not enabled, skipping
2025-06-04 07:35:10.622 [Information] VocomServiceFactory: Phoenix adapter initialization failed, attempting to create standard Vocom driver
2025-06-04 07:35:10.623 [Information] VocomDriver: Initializing Vocom driver
2025-06-04 07:35:10.624 [Information] VocomNativeInterop: Initializing Vocom driver
2025-06-04 07:35:10.632 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-06-04 07:35:10.632 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 07:35:10.632 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 07:35:10.633 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 07:35:10.634 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-06-04 07:35:10.635 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr120.dll
2025-06-04 07:35:10.636 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcp120.dll
2025-06-04 07:35:10.636 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-06-04 07:35:10.637 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcp140.dll
2025-06-04 07:35:10.637 [Warning] WUDFPumaDependencyResolver: Could not load dependency: vcruntime140.dll
2025-06-04 07:35:10.638 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-06-04 07:35:10.640 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-06-04 07:35:10.640 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-06-04 07:35:10.641 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-06-04 07:35:10.642 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-06-04 07:35:10.642 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-06-04 07:35:10.642 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-06-04 07:35:10.642 [Information] VocomDriver: Vocom driver initialized successfully
2025-06-04 07:35:10.644 [Information] VocomService: Initializing Vocom service with dependencies
2025-06-04 07:35:10.645 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-06-04 07:35:10.646 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-06-04 07:35:10.647 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-06-04 07:35:10.691 [Information] WiFiCommunicationService: WiFi is available
2025-06-04 07:35:10.691 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-06-04 07:35:10.692 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-06-04 07:35:10.693 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-06-04 07:35:10.694 [Information] BluetoothCommunicationService: Bluetooth is available
2025-06-04 07:35:10.695 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-06-04 07:35:10.696 [Information] VocomService: Initializing Vocom service
2025-06-04 07:35:10.697 [Information] VocomService: Checking if PTT application is running
2025-06-04 07:35:10.709 [Information] VocomService: PTT application is not running
2025-06-04 07:35:10.711 [Information] VocomService: Vocom service initialized successfully
2025-06-04 07:35:10.712 [Information] VocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-06-04 07:35:10.712 [Information] App: Initializing Vocom service
2025-06-04 07:35:10.712 [Information] VocomService: Initializing Vocom service
2025-06-04 07:35:10.712 [Information] VocomService: Checking if PTT application is running
2025-06-04 07:35:10.722 [Information] VocomService: PTT application is not running
2025-06-04 07:35:10.722 [Information] VocomService: Vocom service initialized successfully
2025-06-04 07:35:10.724 [Information] VocomService: Scanning for Vocom devices
2025-06-04 07:35:10.734 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 07:35:10.757 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 07:35:10.760 [Information] VocomService: Found 2 Vocom devices
2025-06-04 07:35:10.760 [Information] App: Found 2 Vocom devices, attempting to connect to the first one
2025-06-04 07:35:10.762 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 07:35:10.763 [Information] VocomService: Checking if PTT application is running
2025-06-04 07:35:10.793 [Information] VocomService: PTT application is not running
2025-06-04 07:35:10.794 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 07:35:10.795 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-06-04 07:35:11.598 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 07:35:11.599 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 07:35:11.599 [Information] App: Connected to Vocom device Vocom - 88890300 (Bluetooth)
2025-06-04 07:35:11.602 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-06-04 07:35:11.604 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:35:11.605 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-06-04 07:35:11.608 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 07:35:11.610 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 07:35:11.610 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 07:35:11.613 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 07:35:11.615 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 07:35:11.624 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 07:35:11.626 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 07:35:11.628 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 07:35:11.634 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 07:35:11.636 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 07:35:11.648 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 07:35:11.649 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 07:35:11.649 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 07:35:11.649 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 07:35:11.650 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 07:35:11.650 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 07:35:11.650 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 07:35:11.650 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 07:35:11.650 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 07:35:11.654 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 07:35:11.654 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 07:35:11.655 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 07:35:11.655 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 07:35:11.655 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 07:35:11.656 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 07:35:11.656 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 07:35:11.656 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 07:35:11.658 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 07:35:11.660 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.660 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.661 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.661 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.662 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.663 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.669 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.669 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.670 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.671 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-04 07:35:11.672 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-04 07:35:11.673 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 07:35:11.676 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 07:35:11.677 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.677 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.678 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.678 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.678 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.678 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.679 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.679 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.679 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.679 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.680 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.680 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.687 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.687 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.688 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.688 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.688 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.688 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.689 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.689 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.689 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.690 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.690 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.691 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.697 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.697 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.698 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.698 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.698 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.698 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.699 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.699 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.699 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.699 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.700 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.700 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.706 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.706 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.707 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.707 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.707 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.707 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.708 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.708 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.708 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.708 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.709 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.709 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.715 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.715 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.716 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.716 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.716 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.716 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.717 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.717 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.717 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.717 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.718 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.718 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.724 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.724 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.725 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.725 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.725 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.726 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.726 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.726 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.726 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.727 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.727 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.727 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.733 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.733 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.734 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.734 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.734 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.735 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.735 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.735 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.735 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.736 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.736 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.736 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.742 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.743 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.743 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.743 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.744 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.744 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.744 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.744 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.745 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.745 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.746 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.746 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.752 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.752 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.753 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.753 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.753 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.753 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.753 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.754 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.754 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.754 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.754 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.754 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.759 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.760 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.760 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.760 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.760 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.760 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.761 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.761 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.761 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.761 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.761 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.762 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.767 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.767 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.768 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.768 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.768 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.768 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.768 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.769 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.769 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.769 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.769 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.769 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.774 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.774 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.775 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.775 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.775 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.775 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.776 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.776 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.776 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.777 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.777 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.777 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.783 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.784 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.784 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.784 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.785 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.785 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.785 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.786 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.786 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.786 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.786 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.786 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.792 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.792 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.793 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.793 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.793 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.793 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.794 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.794 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.794 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.795 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.795 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.795 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.801 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.801 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.802 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.802 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.802 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.802 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.803 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.803 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.803 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.803 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.804 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.804 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.810 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.810 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.810 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.811 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.811 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.811 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.811 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.812 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.812 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.812 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.813 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.813 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.818 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.818 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.818 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.819 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.819 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.819 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.819 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.819 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.820 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.820 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.820 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.820 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.826 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.826 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.826 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.827 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.827 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.827 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.827 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.827 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.828 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.828 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.828 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.828 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.834 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.834 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.834 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.835 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.835 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.835 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.835 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.835 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.836 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.836 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.836 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.836 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.842 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:11.842 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.842 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.843 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.843 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:11.843 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.843 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.843 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.844 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.844 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.844 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.844 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:11.850 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 07:35:11.851 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-04 07:35:11.853 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 07:35:11.853 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 07:35:11.864 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 07:35:11.865 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 07:35:11.865 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 07:35:11.866 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:11.867 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:11.867 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:11.867 [Information] VocomService: Using generic data transfer
2025-06-04 07:35:11.868 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 07:35:11.869 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 07:35:11.869 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.869 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:11.869 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:11.870 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 07:35:11.871 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:11.872 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 07:35:11.872 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 07:35:11.874 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 07:35:11.874 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 07:35:11.885 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 07:35:11.886 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 07:35:11.886 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 07:35:11.897 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 07:35:11.908 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 07:35:11.919 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 07:35:11.930 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 07:35:11.941 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 07:35:11.942 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 07:35:11.942 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 07:35:11.952 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 07:35:11.953 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 07:35:11.953 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 07:35:11.965 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 07:35:11.975 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 07:35:11.987 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 07:35:11.997 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 07:35:12.008 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 07:35:12.019 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 07:35:12.021 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 07:35:12.021 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 07:35:12.032 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 07:35:12.033 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 07:35:12.033 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 07:35:12.034 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 07:35:12.034 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 07:35:12.034 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 07:35:12.034 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 07:35:12.034 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 07:35:12.035 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 07:35:12.035 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 07:35:12.035 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 07:35:12.035 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 07:35:12.035 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 07:35:12.036 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 07:35:12.036 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 07:35:12.036 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 07:35:12.036 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 07:35:12.137 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 07:35:12.137 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 07:35:12.139 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 07:35:12.140 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:35:12.140 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 07:35:12.141 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 07:35:12.141 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:35:12.141 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 07:35:12.141 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 07:35:12.142 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:35:12.142 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 07:35:12.142 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 07:35:12.142 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:35:12.142 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 07:35:12.143 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 07:35:12.143 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-06-04 07:35:12.146 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-06-04 07:35:12.147 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-06-04 07:35:12.149 [Information] BackupService: Initializing backup service
2025-06-04 07:35:12.150 [Information] BackupService: Backup service initialized successfully
2025-06-04 07:35:12.150 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-06-04 07:35:12.150 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-06-04 07:35:12.152 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-06-04 07:35:12.193 [Information] BackupService: Compressing backup data
2025-06-04 07:35:12.199 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (447 bytes)
2025-06-04 07:35:12.199 [Information] BackupServiceFactory: Created template for category: Production
2025-06-04 07:35:12.200 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-06-04 07:35:12.200 [Information] BackupService: Compressing backup data
2025-06-04 07:35:12.201 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (448 bytes)
2025-06-04 07:35:12.201 [Information] BackupServiceFactory: Created template for category: Development
2025-06-04 07:35:12.201 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-06-04 07:35:12.202 [Information] BackupService: Compressing backup data
2025-06-04 07:35:12.203 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (440 bytes)
2025-06-04 07:35:12.203 [Information] BackupServiceFactory: Created template for category: Testing
2025-06-04 07:35:12.203 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-06-04 07:35:12.204 [Information] BackupService: Compressing backup data
2025-06-04 07:35:12.205 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-06-04 07:35:12.205 [Information] BackupServiceFactory: Created template for category: Archived
2025-06-04 07:35:12.206 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-06-04 07:35:12.206 [Information] BackupService: Compressing backup data
2025-06-04 07:35:12.207 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (446 bytes)
2025-06-04 07:35:12.207 [Information] BackupServiceFactory: Created template for category: Critical
2025-06-04 07:35:12.208 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-06-04 07:35:12.208 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-06-04 07:35:12.209 [Information] BackupService: Compressing backup data
2025-06-04 07:35:12.210 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (512 bytes)
2025-06-04 07:35:12.210 [Information] BackupServiceFactory: Created template with predefined tags
2025-06-04 07:35:12.210 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-06-04 07:35:12.212 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-06-04 07:35:12.215 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 07:35:12.217 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 07:35:12.258 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (1)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 07:35:12.258 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 07:35:12.259 [Information] BackupSchedulerService: Starting backup scheduler
2025-06-04 07:35:12.259 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-06-04 07:35:12.260 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-06-04 07:35:12.260 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-06-04 07:35:12.261 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-06-04 07:35:12.263 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-06-04 07:35:12.263 [Information] App: Flash operation monitor service initialized successfully
2025-06-04 07:35:12.271 [Information] LicensingService: Initializing licensing service
2025-06-04 07:35:12.316 [Error] LicensingService: Error loading license information Exception: Padding is invalid and cannot be removed. StackTrace:    at System.Security.Cryptography.SymmetricPadding.GetPaddingLength(ReadOnlySpan`1 block, PaddingMode paddingMode, Int32 blockSize)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(ReadOnlySpan`1 inputBuffer, Span`1 outputBuffer)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.UniversalCryptoTransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.CryptoStream.ReadAsyncCore(Memory`1 buffer, CancellationToken cancellationToken, Boolean useAsync)
   at System.Security.Cryptography.CryptoStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at System.IO.StreamReader.ReadBuffer()
   at System.IO.StreamReader.ReadToEnd()
   at VolvoFlashWR.Core.Services.LicensingService.DecryptData(Byte[] encryptedData) in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 424
   at VolvoFlashWR.Core.Services.LicensingService.LoadLicenseInfoAsync() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 333
2025-06-04 07:35:12.319 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-06-04 07:35:12.319 [Information] App: Licensing service initialized successfully
2025-06-04 07:35:12.320 [Information] App: License status: Trial
2025-06-04 07:35:12.320 [Information] App: Trial period: 30 days remaining
2025-06-04 07:35:12.321 [Information] BackupSchedulerService: Getting all backup schedules
2025-06-04 07:35:12.490 [Information] VocomService: Initializing Vocom service
2025-06-04 07:35:12.491 [Information] VocomService: Checking if PTT application is running
2025-06-04 07:35:12.499 [Information] VocomService: PTT application is not running
2025-06-04 07:35:12.500 [Information] VocomService: Vocom service initialized successfully
2025-06-04 07:35:12.550 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 07:35:12.551 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 07:35:12.551 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 07:35:12.551 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 07:35:12.551 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 07:35:12.552 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 07:35:12.552 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 07:35:12.553 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 07:35:12.553 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 07:35:12.553 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 07:35:12.564 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 07:35:12.565 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 07:35:12.565 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 07:35:12.565 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 07:35:12.565 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 07:35:12.565 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 07:35:12.566 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 07:35:12.566 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 07:35:12.566 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 07:35:12.566 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 07:35:12.566 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 07:35:12.567 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 07:35:12.567 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 07:35:12.567 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 07:35:12.567 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 07:35:12.567 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 07:35:12.567 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 07:35:12.568 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 07:35:12.568 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.568 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.568 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.568 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.569 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.569 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.569 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.569 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.570 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.570 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-04 07:35:12.570 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-04 07:35:12.570 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 07:35:12.571 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 07:35:12.571 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.571 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.571 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.571 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.571 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.572 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.572 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.572 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.572 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.573 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.573 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.573 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.579 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.580 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.580 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.580 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.580 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.581 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.581 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.581 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.581 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.582 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.582 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.582 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.588 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.589 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.589 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.589 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.590 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.590 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.590 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.591 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.591 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.591 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.592 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.592 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.598 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.598 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.599 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.599 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.599 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.599 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.599 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.600 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.600 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.600 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.601 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.601 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.607 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.607 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.608 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.608 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.608 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.608 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.608 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.609 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.609 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.610 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.610 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.610 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.616 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.617 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.617 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.617 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.617 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.618 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.618 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.618 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.619 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.619 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.620 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.620 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.626 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.626 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.626 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.626 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.627 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.627 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.627 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.627 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.628 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.628 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.629 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.629 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.635 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.636 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.636 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.636 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.637 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.637 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.637 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.637 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.638 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.638 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.639 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.639 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.645 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.646 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.646 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.646 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.647 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.647 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.647 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.647 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.648 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.648 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.648 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.649 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.654 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.655 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.655 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.655 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.656 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.656 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.656 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.656 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.656 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.657 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.657 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.658 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.663 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.664 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.664 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.664 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.665 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.665 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.665 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.666 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.666 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.666 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.667 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.667 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.673 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.674 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.674 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.674 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.674 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.675 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.675 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.675 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.676 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.676 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.676 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.677 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.682 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.683 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.683 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.683 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.683 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.684 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.684 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.684 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.685 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.685 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.685 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.686 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.691 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.692 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.692 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.692 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.692 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.693 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.693 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.693 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.693 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.694 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.694 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.694 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.699 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.700 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.700 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.700 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.700 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.700 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.701 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.701 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.701 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.701 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.702 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.702 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.707 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.708 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.708 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.708 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.708 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.708 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.708 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.709 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.709 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.709 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.709 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.710 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.715 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.716 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.716 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.716 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.716 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.717 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.717 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.717 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.717 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.718 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.718 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.718 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.724 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.724 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.725 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.725 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.725 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.725 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.725 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.726 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.726 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.726 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.726 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.727 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.732 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.733 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.733 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.733 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.734 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.734 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.734 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.735 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.735 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.735 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.736 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.736 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.742 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:35:12.743 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.743 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.743 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.743 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:35:12.743 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.744 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.744 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.744 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.744 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.745 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.745 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:35:12.750 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 07:35:12.751 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-04 07:35:12.751 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 07:35:12.751 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 07:35:12.762 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 07:35:12.762 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 07:35:12.763 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 07:35:12.763 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:35:12.763 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:35:12.763 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:35:12.764 [Information] VocomService: Using generic data transfer
2025-06-04 07:35:12.764 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 07:35:12.764 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 07:35:12.764 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.765 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:35:12.765 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:35:12.765 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 07:35:12.766 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:35:12.766 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 07:35:12.766 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 07:35:12.766 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 07:35:12.767 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 07:35:12.778 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 07:35:12.778 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 07:35:12.779 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 07:35:12.790 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 07:35:12.801 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 07:35:12.812 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 07:35:12.823 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 07:35:12.834 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 07:35:12.834 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 07:35:12.834 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 07:35:12.846 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 07:35:12.846 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 07:35:12.846 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 07:35:12.858 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 07:35:12.868 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 07:35:12.879 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 07:35:12.890 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 07:35:12.901 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 07:35:12.912 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 07:35:12.913 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 07:35:12.913 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 07:35:12.924 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 07:35:12.925 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 07:35:12.925 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 07:35:12.925 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 07:35:12.925 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 07:35:12.925 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 07:35:12.926 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 07:35:12.926 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 07:35:12.926 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 07:35:12.926 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 07:35:12.926 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 07:35:12.927 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 07:35:12.927 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 07:35:12.927 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 07:35:12.927 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 07:35:12.927 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 07:35:12.927 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 07:35:13.028 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 07:35:13.028 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 07:35:13.029 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 07:35:13.029 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:35:13.029 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 07:35:13.029 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 07:35:13.030 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:35:13.030 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 07:35:13.030 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 07:35:13.030 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:35:13.031 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 07:35:13.031 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 07:35:13.031 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:35:13.031 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 07:35:13.032 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 07:35:13.083 [Information] BackupService: Initializing backup service
2025-06-04 07:35:13.083 [Information] BackupService: Backup service initialized successfully
2025-06-04 07:35:13.134 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 07:35:13.134 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 07:35:13.135 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (1)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 07:35:13.136 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 07:35:13.187 [Information] BackupService: Getting predefined backup categories
2025-06-04 07:35:13.239 [Information] MainViewModel: Services initialized successfully
2025-06-04 07:35:13.241 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 07:35:13.242 [Information] VocomService: Scanning for Vocom devices
2025-06-04 07:35:13.242 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 07:35:13.242 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 07:35:13.244 [Information] VocomService: Found 2 Vocom devices
2025-06-04 07:35:13.245 [Information] MainViewModel: Found 2 Vocom device(s)
2025-06-04 07:35:15.252 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 07:35:15.253 [Information] VocomService: Scanning for Vocom devices
2025-06-04 07:35:15.254 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 07:35:15.254 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 07:35:15.255 [Information] VocomService: Found 2 Vocom devices
2025-06-04 07:35:15.256 [Information] MainViewModel: Found 2 Vocom device(s)
2025-06-04 07:35:27.258 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 07:35:27.259 [Information] VocomService: Scanning for Vocom devices
2025-06-04 07:35:27.259 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 07:35:27.259 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 07:35:27.265 [Information] VocomService: Found 2 Vocom devices
2025-06-04 07:35:27.266 [Information] MainViewModel: Found 2 Vocom device(s)
2025-06-04 07:37:30.283 [Information] LoggingService: Logging service initialized
2025-06-04 07:37:30.295 [Information] AppConfigurationService: Initializing configuration service
2025-06-04 07:37:30.296 [Information] AppConfigurationService: Created configuration directory: C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (1)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Config
2025-06-04 07:37:30.297 [Information] AppConfigurationService: Configuration file not found, creating default
2025-06-04 07:37:30.300 [Warning] AppConfigurationService: Configuration service not initialized
2025-06-04 07:37:30.301 [Information] AppConfigurationService: Default configuration created
2025-06-04 07:37:30.301 [Information] AppConfigurationService: Configuration service initialized successfully
2025-06-04 07:37:30.302 [Information] App: Configuration service initialized successfully
2025-06-04 07:37:30.303 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-06-04 07:37:30.303 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: ''
2025-06-04 07:37:30.304 [Information] App: Environment variable exists: False, not 'false': True
2025-06-04 07:37:30.305 [Information] App: Final useDummyImplementations value: False
2025-06-04 07:37:30.306 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: ''
2025-06-04 07:37:30.306 [Information] App: usePatchedImplementation flag is: False
2025-06-04 07:37:30.307 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: ''
2025-06-04 07:37:30.307 [Information] App: APCI_LIBRARY_PATH environment variable is set to: ''
2025-06-04 07:37:30.308 [Information] App: VERBOSE_LOGGING environment variable is set to: ''
2025-06-04 07:37:30.308 [Information] App: verboseLogging flag is: False
2025-06-04 07:37:30.309 [Information] App: Verifying real hardware requirements...
2025-06-04 07:37:30.310 [Warning] App: ✗ Missing critical library: WUDFPuma.dll at C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (1)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\WUDFPuma.dll
2025-06-04 07:37:30.310 [Warning] App: ✗ Missing critical library: apci.dll at C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (1)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\apci.dll
2025-06-04 07:37:30.310 [Warning] App: ✗ Missing critical library: Volvo.ApciPlus.dll at C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (1)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\Volvo.ApciPlus.dll
2025-06-04 07:37:30.311 [Warning] App: ✗ Missing critical library: Volvo.ApciPlusData.dll at C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (1)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\Volvo.ApciPlusData.dll
2025-06-04 07:37:30.311 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 07:37:30.311 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-06-04 07:37:30.312 [Information] App: ✓ Found Vocom driver config: C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (1)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Vocom\config.json
2025-06-04 07:37:30.312 [Warning] App: ⚠ Some real hardware requirements are missing - application may fall back to dummy mode
2025-06-04 07:37:30.333 [Information] App: Creating standard VocomServiceFactory instance
2025-06-04 07:37:30.333 [Information] App: Successfully created standard VocomServiceFactory instance
2025-06-04 07:37:30.334 [Information] App: Using VolvoFlashWR.Communication.Vocom.VocomServiceFactory Vocom service factory
2025-06-04 07:37:30.334 [Information] App: Checking if PTT application is running before creating Vocom service
2025-06-04 07:37:30.369 [Information] App: Creating Vocom service (attempt 1/3)
2025-06-04 07:37:30.374 [Information] VocomServiceFactory: Creating Vocom service with default settings
2025-06-04 07:37:30.375 [Information] VocomServiceFactory: Phoenix Vocom adapter not enabled, skipping
2025-06-04 07:37:30.375 [Information] VocomServiceFactory: Phoenix adapter initialization failed, attempting to create standard Vocom driver
2025-06-04 07:37:30.376 [Information] VocomDriver: Initializing Vocom driver
2025-06-04 07:37:30.378 [Information] VocomNativeInterop: Initializing Vocom driver
2025-06-04 07:37:30.382 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-06-04 07:37:30.382 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 07:37:30.382 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 07:37:30.383 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 07:37:30.384 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-06-04 07:37:30.386 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr120.dll
2025-06-04 07:37:30.387 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcp120.dll
2025-06-04 07:37:30.389 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-06-04 07:37:30.390 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcp140.dll
2025-06-04 07:37:30.391 [Warning] WUDFPumaDependencyResolver: Could not load dependency: vcruntime140.dll
2025-06-04 07:37:30.392 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-06-04 07:37:30.394 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-06-04 07:37:30.395 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-06-04 07:37:30.395 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-06-04 07:37:30.396 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-06-04 07:37:30.396 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-06-04 07:37:30.397 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-06-04 07:37:30.397 [Information] VocomDriver: Vocom driver initialized successfully
2025-06-04 07:37:30.399 [Information] VocomService: Initializing Vocom service with dependencies
2025-06-04 07:37:30.400 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-06-04 07:37:30.400 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-06-04 07:37:30.401 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-06-04 07:37:30.451 [Information] WiFiCommunicationService: WiFi is available
2025-06-04 07:37:30.452 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-06-04 07:37:30.453 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-06-04 07:37:30.455 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-06-04 07:37:30.458 [Information] BluetoothCommunicationService: Bluetooth is available
2025-06-04 07:37:30.458 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-06-04 07:37:30.459 [Information] VocomService: Initializing Vocom service
2025-06-04 07:37:30.461 [Information] VocomService: Checking if PTT application is running
2025-06-04 07:37:30.475 [Information] VocomService: PTT application is not running
2025-06-04 07:37:30.478 [Information] VocomService: Vocom service initialized successfully
2025-06-04 07:37:30.478 [Information] VocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-06-04 07:37:30.479 [Information] App: Initializing Vocom service
2025-06-04 07:37:30.479 [Information] VocomService: Initializing Vocom service
2025-06-04 07:37:30.479 [Information] VocomService: Checking if PTT application is running
2025-06-04 07:37:30.491 [Information] VocomService: PTT application is not running
2025-06-04 07:37:30.492 [Information] VocomService: Vocom service initialized successfully
2025-06-04 07:37:30.493 [Information] VocomService: Scanning for Vocom devices
2025-06-04 07:37:30.496 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 07:37:30.518 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 07:37:30.521 [Information] VocomService: Found 2 Vocom devices
2025-06-04 07:37:30.522 [Information] App: Found 2 Vocom devices, attempting to connect to the first one
2025-06-04 07:37:30.525 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 07:37:30.526 [Information] VocomService: Checking if PTT application is running
2025-06-04 07:37:30.537 [Information] VocomService: PTT application is not running
2025-06-04 07:37:30.540 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 07:37:30.541 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-06-04 07:37:31.345 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 07:37:31.346 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 07:37:31.346 [Information] App: Connected to Vocom device Vocom - 88890300 (Bluetooth)
2025-06-04 07:37:31.348 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-06-04 07:37:31.350 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:37:31.351 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-06-04 07:37:31.353 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 07:37:31.355 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 07:37:31.355 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 07:37:31.357 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 07:37:31.359 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 07:37:31.367 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 07:37:31.369 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 07:37:31.370 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 07:37:31.375 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 07:37:31.377 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 07:37:31.387 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 07:37:31.388 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 07:37:31.389 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 07:37:31.389 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 07:37:31.389 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 07:37:31.390 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 07:37:31.390 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 07:37:31.390 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 07:37:31.390 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 07:37:31.394 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 07:37:31.395 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 07:37:31.395 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 07:37:31.395 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 07:37:31.396 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 07:37:31.396 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 07:37:31.396 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 07:37:31.397 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 07:37:31.399 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 07:37:31.402 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.403 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.403 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.403 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.404 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.405 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.407 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.408 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.409 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.410 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-04 07:37:31.411 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-04 07:37:31.411 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 07:37:31.413 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 07:37:31.415 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.415 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.415 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.415 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.416 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.416 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.416 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.416 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.417 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.417 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.417 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.418 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.424 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.425 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.425 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.425 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.426 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.426 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.426 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.426 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.427 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.427 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.427 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.427 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.433 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.433 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.433 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.434 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.434 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.434 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.434 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.434 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.435 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.435 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.435 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.435 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.441 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.441 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.441 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.441 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.442 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.442 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.442 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.442 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.443 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.443 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.443 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.443 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.448 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.448 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.448 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.449 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.449 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.449 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.449 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.449 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.450 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.450 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.450 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.450 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.455 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.455 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.455 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.456 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.456 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.456 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.456 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.457 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.457 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.457 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.457 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.457 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.463 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.463 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.463 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.464 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.464 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.464 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.464 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.464 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.465 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.465 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.465 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.465 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.472 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.472 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.472 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.473 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.473 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.473 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.473 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.474 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.474 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.474 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.475 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.475 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.481 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.481 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.482 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.482 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.482 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.482 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.483 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.483 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.483 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.484 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.484 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.484 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.490 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.490 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.490 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.490 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.491 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.491 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.491 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.491 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.491 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.492 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.492 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.492 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.497 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.497 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.497 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.498 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.498 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.498 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.498 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.498 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.499 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.499 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.499 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.499 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.505 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.505 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.505 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.506 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.506 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.506 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.506 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.507 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.507 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.507 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.507 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.508 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.514 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.514 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.515 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.515 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.515 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.516 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.516 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.516 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.516 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.517 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.517 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.517 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.522 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.523 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.523 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.523 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.523 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.524 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.524 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.524 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.524 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.525 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.525 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.525 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.531 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.532 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.532 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.533 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.533 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.533 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.534 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.534 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.534 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.534 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.535 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.535 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.541 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.541 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.542 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.542 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.542 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.542 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.542 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.542 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.543 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.543 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.543 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.543 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.549 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.549 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.550 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.550 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.550 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.550 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.550 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.551 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.551 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.551 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.551 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.552 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.557 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.557 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.557 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.558 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.558 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.558 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.558 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.558 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.559 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.559 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.559 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.559 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.565 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.565 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.565 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.566 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.566 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.566 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.566 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.566 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.567 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.567 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.567 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.567 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.573 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:31.573 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.574 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.574 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.574 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:31.574 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.574 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.575 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.575 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.575 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.575 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.575 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:31.581 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 07:37:31.582 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-04 07:37:31.584 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 07:37:31.584 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 07:37:31.595 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 07:37:31.596 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 07:37:31.596 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 07:37:31.597 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:31.598 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:31.598 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:31.598 [Information] VocomService: Using generic data transfer
2025-06-04 07:37:31.600 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 07:37:31.600 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 07:37:31.600 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.600 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:31.601 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:31.602 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 07:37:31.602 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:31.603 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 07:37:31.603 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 07:37:31.605 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 07:37:31.605 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 07:37:31.616 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 07:37:31.617 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 07:37:31.617 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 07:37:31.628 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 07:37:31.639 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 07:37:31.650 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 07:37:31.661 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 07:37:31.672 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 07:37:31.673 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 07:37:31.673 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 07:37:31.684 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 07:37:31.684 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 07:37:31.685 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 07:37:31.695 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 07:37:31.706 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 07:37:31.717 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 07:37:31.728 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 07:37:31.738 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 07:37:31.749 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 07:37:31.751 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 07:37:31.751 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 07:37:31.761 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 07:37:31.762 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 07:37:31.763 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 07:37:31.763 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 07:37:31.763 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 07:37:31.763 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 07:37:31.763 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 07:37:31.764 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 07:37:31.764 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 07:37:31.764 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 07:37:31.764 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 07:37:31.764 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 07:37:31.765 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 07:37:31.765 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 07:37:31.765 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 07:37:31.765 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 07:37:31.766 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 07:37:31.866 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 07:37:31.867 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 07:37:31.869 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 07:37:31.870 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:37:31.871 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 07:37:31.871 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 07:37:31.871 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:37:31.871 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 07:37:31.872 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 07:37:31.872 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:37:31.872 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 07:37:31.872 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 07:37:31.873 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:37:31.873 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 07:37:31.873 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 07:37:31.874 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-06-04 07:37:31.876 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-06-04 07:37:31.877 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-06-04 07:37:31.880 [Information] BackupService: Initializing backup service
2025-06-04 07:37:31.880 [Information] BackupService: Backup service initialized successfully
2025-06-04 07:37:31.880 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-06-04 07:37:31.880 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-06-04 07:37:31.882 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-06-04 07:37:31.924 [Information] BackupService: Compressing backup data
2025-06-04 07:37:31.930 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (448 bytes)
2025-06-04 07:37:31.931 [Information] BackupServiceFactory: Created template for category: Production
2025-06-04 07:37:31.931 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-06-04 07:37:31.932 [Information] BackupService: Compressing backup data
2025-06-04 07:37:31.932 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (452 bytes)
2025-06-04 07:37:31.933 [Information] BackupServiceFactory: Created template for category: Development
2025-06-04 07:37:31.933 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-06-04 07:37:31.933 [Information] BackupService: Compressing backup data
2025-06-04 07:37:31.934 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (445 bytes)
2025-06-04 07:37:31.934 [Information] BackupServiceFactory: Created template for category: Testing
2025-06-04 07:37:31.934 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-06-04 07:37:31.935 [Information] BackupService: Compressing backup data
2025-06-04 07:37:31.936 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-06-04 07:37:31.936 [Information] BackupServiceFactory: Created template for category: Archived
2025-06-04 07:37:31.936 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-06-04 07:37:31.937 [Information] BackupService: Compressing backup data
2025-06-04 07:37:31.938 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (449 bytes)
2025-06-04 07:37:31.938 [Information] BackupServiceFactory: Created template for category: Critical
2025-06-04 07:37:31.938 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-06-04 07:37:31.939 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-06-04 07:37:31.939 [Information] BackupService: Compressing backup data
2025-06-04 07:37:31.940 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-06-04 07:37:31.941 [Information] BackupServiceFactory: Created template with predefined tags
2025-06-04 07:37:31.941 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-06-04 07:37:31.943 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-06-04 07:37:31.945 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 07:37:31.947 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 07:37:31.990 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (1)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 07:37:31.991 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 07:37:31.992 [Information] BackupSchedulerService: Starting backup scheduler
2025-06-04 07:37:31.992 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-06-04 07:37:31.993 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-06-04 07:37:31.993 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-06-04 07:37:31.994 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-06-04 07:37:31.996 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-06-04 07:37:31.996 [Information] App: Flash operation monitor service initialized successfully
2025-06-04 07:37:32.003 [Information] LicensingService: Initializing licensing service
2025-06-04 07:37:32.038 [Error] LicensingService: Error loading license information Exception: Padding is invalid and cannot be removed. StackTrace:    at System.Security.Cryptography.SymmetricPadding.GetPaddingLength(ReadOnlySpan`1 block, PaddingMode paddingMode, Int32 blockSize)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(ReadOnlySpan`1 inputBuffer, Span`1 outputBuffer)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.UniversalCryptoTransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.CryptoStream.ReadAsyncCore(Memory`1 buffer, CancellationToken cancellationToken, Boolean useAsync)
   at System.Security.Cryptography.CryptoStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at System.IO.StreamReader.ReadBuffer()
   at System.IO.StreamReader.ReadToEnd()
   at VolvoFlashWR.Core.Services.LicensingService.DecryptData(Byte[] encryptedData) in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 424
   at VolvoFlashWR.Core.Services.LicensingService.LoadLicenseInfoAsync() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 333
2025-06-04 07:37:32.040 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-06-04 07:37:32.040 [Information] App: Licensing service initialized successfully
2025-06-04 07:37:32.041 [Information] App: License status: Trial
2025-06-04 07:37:32.041 [Information] App: Trial period: 30 days remaining
2025-06-04 07:37:32.041 [Information] BackupSchedulerService: Getting all backup schedules
2025-06-04 07:37:32.205 [Information] VocomService: Initializing Vocom service
2025-06-04 07:37:32.206 [Information] VocomService: Checking if PTT application is running
2025-06-04 07:37:32.215 [Information] VocomService: PTT application is not running
2025-06-04 07:37:32.216 [Information] VocomService: Vocom service initialized successfully
2025-06-04 07:37:32.266 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 07:37:32.267 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 07:37:32.267 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 07:37:32.267 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 07:37:32.268 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 07:37:32.269 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 07:37:32.269 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 07:37:32.270 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 07:37:32.271 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 07:37:32.271 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 07:37:32.281 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 07:37:32.282 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 07:37:32.282 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 07:37:32.282 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 07:37:32.282 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 07:37:32.283 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 07:37:32.283 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 07:37:32.283 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 07:37:32.283 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 07:37:32.284 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 07:37:32.284 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 07:37:32.284 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 07:37:32.284 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 07:37:32.284 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 07:37:32.285 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 07:37:32.285 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 07:37:32.285 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 07:37:32.285 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 07:37:32.285 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.286 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.286 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.286 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.286 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.286 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.287 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.287 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.287 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.288 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-04 07:37:32.288 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-04 07:37:32.288 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 07:37:32.288 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 07:37:32.289 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.289 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.289 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.289 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.289 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.290 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.290 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.290 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.290 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.291 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.291 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.292 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.298 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.299 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.299 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.299 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.299 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.300 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.300 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.300 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.300 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.301 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.301 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.301 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.306 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.307 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.307 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.307 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.307 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.308 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.308 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.308 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.308 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.309 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.309 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.309 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.314 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.315 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.315 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.315 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.316 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.316 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.316 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.317 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.317 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.317 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.318 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.318 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.323 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.324 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.324 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.324 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.324 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.325 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.325 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.325 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.326 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.326 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.326 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.326 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.331 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.332 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.332 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.332 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.332 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.332 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.333 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.333 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.333 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.334 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.334 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.335 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.341 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.342 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.342 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.343 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.343 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.343 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.343 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.344 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.344 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.344 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.345 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.345 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.351 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.351 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.352 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.352 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.352 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.352 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.353 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.353 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.353 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.354 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.355 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.355 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.361 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.361 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.362 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.362 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.362 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.362 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.362 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.363 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.363 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.364 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.364 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.364 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.370 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.371 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.371 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.371 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.371 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.372 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.372 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.372 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.372 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.373 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.373 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.373 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.379 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.380 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.380 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.380 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.380 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.381 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.381 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.381 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.381 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.382 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.382 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.382 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.388 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.388 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.389 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.389 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.389 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.389 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.390 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.390 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.390 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.391 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.391 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.391 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.397 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.398 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.398 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.398 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.398 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.399 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.399 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.400 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.400 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.400 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.401 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.401 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.406 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.406 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.407 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.407 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.407 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.408 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.408 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.408 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.409 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.409 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.409 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.410 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.416 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.416 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.417 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.417 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.417 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.417 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.418 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.418 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.418 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.419 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.419 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.419 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.425 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.425 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.426 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.426 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.426 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.427 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.427 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.427 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.428 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.428 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.428 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.429 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.435 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.436 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.436 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.436 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.436 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.437 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.437 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.437 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.437 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.438 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.438 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.439 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.445 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.445 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.445 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.445 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.446 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.446 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.446 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.446 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.447 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.447 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.447 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.447 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.454 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.454 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.454 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.454 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.455 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.455 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.455 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.455 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.456 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.456 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.456 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.457 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.463 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 07:37:32.463 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.463 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.463 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.464 [Information] VocomService: Detected CAN protocol request
2025-06-04 07:37:32.464 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.464 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.464 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.465 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.465 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.465 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.466 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 07:37:32.472 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 07:37:32.473 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-04 07:37:32.474 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 07:37:32.474 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 07:37:32.484 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 07:37:32.484 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 07:37:32.485 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 07:37:32.485 [Information] VocomService: Sending data and waiting for response
2025-06-04 07:37:32.485 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 07:37:32.485 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 07:37:32.486 [Information] VocomService: Using generic data transfer
2025-06-04 07:37:32.486 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 07:37:32.486 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 07:37:32.487 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.487 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 07:37:32.487 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 07:37:32.488 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 07:37:32.488 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 07:37:32.488 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 07:37:32.489 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 07:37:32.489 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 07:37:32.489 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 07:37:32.500 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 07:37:32.500 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 07:37:32.501 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 07:37:32.512 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 07:37:32.523 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 07:37:32.534 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 07:37:32.545 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 07:37:32.555 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 07:37:32.556 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 07:37:32.556 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 07:37:32.567 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 07:37:32.567 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 07:37:32.567 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 07:37:32.578 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 07:37:32.589 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 07:37:32.600 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 07:37:32.611 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 07:37:32.622 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 07:37:32.633 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 07:37:32.633 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 07:37:32.633 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 07:37:32.644 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 07:37:32.644 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 07:37:32.645 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 07:37:32.645 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 07:37:32.645 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 07:37:32.645 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 07:37:32.645 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 07:37:32.646 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 07:37:32.646 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 07:37:32.646 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 07:37:32.646 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 07:37:32.646 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 07:37:32.647 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 07:37:32.647 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 07:37:32.647 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 07:37:32.647 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 07:37:32.647 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 07:37:32.748 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 07:37:32.748 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 07:37:32.748 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 07:37:32.749 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:37:32.749 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 07:37:32.749 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 07:37:32.750 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:37:32.750 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 07:37:32.750 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 07:37:32.750 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:37:32.750 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 07:37:32.751 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 07:37:32.751 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 07:37:32.751 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 07:37:32.751 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 07:37:32.802 [Information] BackupService: Initializing backup service
2025-06-04 07:37:32.802 [Information] BackupService: Backup service initialized successfully
2025-06-04 07:37:32.853 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 07:37:32.853 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 07:37:32.854 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (1)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 07:37:32.854 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 07:37:32.906 [Information] BackupService: Getting predefined backup categories
2025-06-04 07:37:32.957 [Information] MainViewModel: Services initialized successfully
2025-06-04 07:37:32.959 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 07:37:32.960 [Information] VocomService: Scanning for Vocom devices
2025-06-04 07:37:32.960 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 07:37:32.961 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 07:37:32.962 [Information] VocomService: Found 2 Vocom devices
2025-06-04 07:37:32.963 [Information] MainViewModel: Found 2 Vocom device(s)
2025-06-04 07:37:33.969 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 07:37:33.970 [Information] VocomService: Scanning for Vocom devices
2025-06-04 07:37:33.970 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 07:37:33.971 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 07:37:33.972 [Information] VocomService: Found 2 Vocom devices
2025-06-04 07:37:33.972 [Information] MainViewModel: Found 2 Vocom device(s)
