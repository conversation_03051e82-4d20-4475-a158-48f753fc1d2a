@echo off
setlocal enabledelayedexpansion

echo ========================================
echo VolvoFlashWR Pre-Export Verification
echo ========================================
echo.
echo This script verifies that all necessary files are present
echo before creating the export package.
echo.

set VERIFICATION_FAILED=0

echo Checking critical application files...

REM Check if solution builds successfully
echo.
echo [1/8] Checking if solution can be built...
dotnet build --configuration Release --verbosity quiet --no-restore >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ✗ Solution build failed
    echo   Run 'dotnet build --configuration Release' to see errors
    set VERIFICATION_FAILED=1
) else (
    echo ✓ Solution builds successfully
)

REM Check main executable
echo.
echo [2/8] Checking main executable...
if exist "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\VolvoFlashWR.Launcher.exe" (
    echo ✓ Main executable found
) else (
    echo ✗ Main executable not found
    echo   Expected: VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\VolvoFlashWR.Launcher.exe
    set VERIFICATION_FAILED=1
)

REM Check critical libraries
echo.
echo [3/8] Checking critical libraries...
set CRITICAL_LIBS=WUDFPuma.dll apci.dll Volvo.ApciPlus.dll Volvo.ApciPlusData.dll
for %%L in (%CRITICAL_LIBS%) do (
    if exist "Libraries\%%L" (
        echo ✓ %%L found
    ) else (
        echo ✗ %%L not found in Libraries folder
        set VERIFICATION_FAILED=1
    )
)

REM Check MC9S12XEP100 documentation
echo.
echo [4/8] Checking MC9S12XEP100 documentation...
if exist "MC9S12XEP100RMV1-1358561" (
    echo ✓ MC9S12XEP100 documentation folder found
    dir "MC9S12XEP100RMV1-1358561\*.txt" >nul 2>&1
    if %ERRORLEVEL% equ 0 (
        echo ✓ Documentation files found
    ) else (
        echo ✗ No documentation files found
        set VERIFICATION_FAILED=1
    )
) else (
    echo ✗ MC9S12XEP100 documentation folder not found
    set VERIFICATION_FAILED=1
)

REM Check driver configurations
echo.
echo [5/8] Checking driver configurations...
if exist "Drivers\Vocom" (
    echo ✓ Vocom driver configuration found
) else (
    echo ⚠ Vocom driver configuration not found (will be created during export)
)

if exist "Drivers\MC9S12XEP100" (
    echo ✓ MC9S12XEP100 driver configuration found
) else (
    echo ⚠ MC9S12XEP100 driver configuration not found (will be created during export)
)

REM Check .NET 8.0 compatibility
echo.
echo [6/8] Checking .NET 8.0 compatibility...
dotnet --version | findstr "8." >nul
if %ERRORLEVEL% equ 0 (
    echo ✓ .NET 8.0 SDK is available
) else (
    echo ✗ .NET 8.0 SDK not found
    echo   Please install .NET 8.0 SDK for building
    set VERIFICATION_FAILED=1
)

REM Check project target frameworks
echo.
echo [7/8] Checking project target frameworks...
findstr "net8.0-windows" VolvoFlashWR.Launcher\VolvoFlashWR.Launcher.csproj >nul
if %ERRORLEVEL% equ 0 (
    echo ✓ Launcher targets net8.0-windows
) else (
    echo ✗ Launcher does not target net8.0-windows
    set VERIFICATION_FAILED=1
)

findstr "win-x64" VolvoFlashWR.Launcher\VolvoFlashWR.Launcher.csproj >nul
if %ERRORLEVEL% equ 0 (
    echo ✓ Launcher targets win-x64 platform
) else (
    echo ⚠ Launcher platform target not explicitly set to win-x64
)

REM Check for recent modifications
echo.
echo [8/8] Checking for recent modifications...
if exist "VolvoFlashWR.Communication\Protocols\CANProtocolHandler.cs" (
    findstr "GetDefaultCANConfiguration" VolvoFlashWR.Communication\Protocols\CANProtocolHandler.cs >nul
    if %ERRORLEVEL% equ 0 (
        echo ✓ Recent infinite loop fix is present
    ) else (
        echo ✗ Recent infinite loop fix not found
        echo   The CANProtocolHandler may still have the infinite loop issue
        set VERIFICATION_FAILED=1
    )
) else (
    echo ✗ CANProtocolHandler.cs not found
    set VERIFICATION_FAILED=1
)

echo.
echo ========================================
echo Verification Summary
echo ========================================

if %VERIFICATION_FAILED% equ 0 (
    echo.
    echo ✅ ALL CHECKS PASSED!
    echo.
    echo The application is ready for export. You can now run:
    echo Export_For_Real_Hardware_Testing.bat
    echo.
    echo This will create a complete package for real hardware testing
    echo with all necessary files, libraries, and documentation.
    echo.
) else (
    echo.
    echo ❌ VERIFICATION FAILED!
    echo.
    echo Please fix the issues above before running the export script.
    echo.
    echo Common fixes:
    echo - Run: dotnet build --configuration Release
    echo - Ensure all libraries are in the Libraries folder
    echo - Verify .NET 8.0 SDK is installed
    echo - Check that recent code fixes are applied
    echo.
)

echo.
echo Additional Information:
echo.
echo Export will include:
echo ✓ Application files (Release build, x64)
echo ✓ All Vocom and APCI libraries
echo ✓ MC9S12XEP100 microcontroller documentation
echo ✓ Driver configurations
echo ✓ Launcher scripts for easy startup
echo ✓ System requirements checker
echo ✓ Installation script for target system
echo ✓ Comprehensive troubleshooting documentation
echo.
echo The exported package will be fully self-contained and ready
echo for testing with real Vocom hardware on another laptop.
echo.

pause
