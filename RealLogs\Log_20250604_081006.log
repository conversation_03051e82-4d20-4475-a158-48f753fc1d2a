Log started at 6/4/2025 8:10:06 AM
2025-06-04 08:10:06.768 [Information] LoggingService: Logging service initialized
2025-06-04 08:10:06.775 [Information] AppConfigurationService: Initializing configuration service
2025-06-04 08:10:06.775 [Information] AppConfigurationService: Created configuration directory: C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Config
2025-06-04 08:10:06.776 [Information] AppConfigurationService: Configuration file not found, creating default
2025-06-04 08:10:06.783 [Warning] AppConfigurationService: Configuration service not initialized
2025-06-04 08:10:06.784 [Information] AppConfigurationService: Default configuration created
2025-06-04 08:10:06.784 [Information] AppConfigurationService: Configuration service initialized successfully
2025-06-04 08:10:06.785 [Information] App: Configuration service initialized successfully
2025-06-04 08:10:06.785 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-06-04 08:10:06.785 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: ''
2025-06-04 08:10:06.786 [Information] App: Environment variable exists: False, not 'false': True
2025-06-04 08:10:06.786 [Information] App: Final useDummyImplementations value: False
2025-06-04 08:10:06.786 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: ''
2025-06-04 08:10:06.786 [Information] App: usePatchedImplementation flag is: False
2025-06-04 08:10:06.786 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: ''
2025-06-04 08:10:06.787 [Information] App: APCI_LIBRARY_PATH environment variable is set to: ''
2025-06-04 08:10:06.787 [Information] App: VERBOSE_LOGGING environment variable is set to: ''
2025-06-04 08:10:06.787 [Information] App: verboseLogging flag is: False
2025-06-04 08:10:06.788 [Information] App: Verifying real hardware requirements...
2025-06-04 08:10:06.788 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-06-04 08:10:06.789 [Information] App: ✓ Found critical library: apci.dll
2025-06-04 08:10:06.789 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-06-04 08:10:06.789 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-06-04 08:10:06.789 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 08:10:06.789 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-06-04 08:10:06.790 [Information] App: ✓ Found Vocom driver config: C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Vocom\config.json
2025-06-04 08:10:06.790 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-06-04 08:10:06.801 [Information] App: Creating standard VocomServiceFactory instance
2025-06-04 08:10:06.802 [Information] App: Successfully created standard VocomServiceFactory instance
2025-06-04 08:10:06.802 [Information] App: Using VolvoFlashWR.Communication.Vocom.VocomServiceFactory Vocom service factory
2025-06-04 08:10:06.802 [Information] App: Checking if PTT application is running before creating Vocom service
2025-06-04 08:10:06.839 [Information] App: Creating Vocom service (attempt 1/3)
2025-06-04 08:10:06.841 [Information] VocomServiceFactory: Creating Vocom service with default settings
2025-06-04 08:10:06.841 [Information] VocomServiceFactory: Phoenix Vocom adapter not enabled, skipping
2025-06-04 08:10:06.841 [Information] VocomServiceFactory: Phoenix adapter initialization failed, attempting to create standard Vocom driver
2025-06-04 08:10:06.842 [Information] VocomDriver: Initializing Vocom driver
2025-06-04 08:10:06.844 [Information] VocomNativeInterop: Initializing Vocom driver
2025-06-04 08:10:06.851 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-06-04 08:10:06.852 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 08:10:06.852 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 08:10:06.853 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 08:10:06.853 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-06-04 08:10:06.855 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr120.dll
2025-06-04 08:10:06.855 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcp120.dll
2025-06-04 08:10:06.856 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-06-04 08:10:06.856 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcp140.dll
2025-06-04 08:10:06.857 [Warning] WUDFPumaDependencyResolver: Could not load dependency: vcruntime140.dll
2025-06-04 08:10:06.857 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-06-04 08:10:06.859 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-06-04 08:10:06.860 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-06-04 08:10:06.861 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-06-04 08:10:06.861 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-06-04 08:10:06.861 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-06-04 08:10:06.862 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-06-04 08:10:06.862 [Information] VocomDriver: Vocom driver initialized successfully
2025-06-04 08:10:06.864 [Information] VocomService: Initializing Vocom service with dependencies
2025-06-04 08:10:06.865 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-06-04 08:10:06.865 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-06-04 08:10:06.866 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-06-04 08:10:06.908 [Information] WiFiCommunicationService: WiFi is available
2025-06-04 08:10:06.909 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-06-04 08:10:06.910 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-06-04 08:10:06.911 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-06-04 08:10:06.912 [Information] BluetoothCommunicationService: Bluetooth is available
2025-06-04 08:10:06.913 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-06-04 08:10:06.913 [Information] VocomService: Initializing Vocom service
2025-06-04 08:10:06.915 [Information] VocomService: Checking if PTT application is running
2025-06-04 08:10:06.924 [Information] VocomService: PTT application is not running
2025-06-04 08:10:06.927 [Information] VocomService: Vocom service initialized successfully
2025-06-04 08:10:06.928 [Information] VocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-06-04 08:10:06.929 [Information] App: Initializing Vocom service
2025-06-04 08:10:06.929 [Information] VocomService: Initializing Vocom service
2025-06-04 08:10:06.929 [Information] VocomService: Checking if PTT application is running
2025-06-04 08:10:06.940 [Information] VocomService: PTT application is not running
2025-06-04 08:10:06.940 [Information] VocomService: Vocom service initialized successfully
2025-06-04 08:10:06.942 [Information] VocomService: Scanning for Vocom devices
2025-06-04 08:10:06.949 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 08:10:06.970 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 08:10:06.973 [Information] VocomService: Found 2 Vocom devices
2025-06-04 08:10:06.973 [Information] App: Found 2 Vocom devices, attempting to connect to the first one
2025-06-04 08:10:06.975 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 08:10:06.975 [Information] VocomService: Checking if PTT application is running
2025-06-04 08:10:06.985 [Information] VocomService: PTT application is not running
2025-06-04 08:10:06.987 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 08:10:06.988 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-06-04 08:10:07.791 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 08:10:07.792 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 08:10:07.792 [Information] App: Connected to Vocom device Vocom - 88890300 (Bluetooth)
2025-06-04 08:10:07.795 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-06-04 08:10:07.798 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:07.799 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-06-04 08:10:07.802 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 08:10:07.803 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 08:10:07.804 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 08:10:07.806 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 08:10:07.807 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 08:10:07.819 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 08:10:07.821 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 08:10:07.824 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 08:10:07.831 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 08:10:07.833 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 08:10:07.845 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 08:10:07.846 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 08:10:07.846 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 08:10:07.847 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 08:10:07.847 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 08:10:07.847 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 08:10:07.847 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 08:10:07.848 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 08:10:07.848 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 08:10:07.851 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 08:10:07.851 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 08:10:07.851 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 08:10:07.851 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 08:10:07.852 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 08:10:07.852 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 08:10:07.852 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 08:10:07.852 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 08:10:07.854 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 08:10:07.856 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:07.857 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:07.857 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:07.857 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:07.858 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.859 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.866 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:07.867 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.868 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.868 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-04 08:10:07.869 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-04 08:10:07.869 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 08:10:07.872 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 08:10:07.873 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:07.873 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:07.873 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:07.873 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:07.874 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:07.874 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.874 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.874 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:07.874 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.875 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.875 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:07.876 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:07.883 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:07.883 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:07.884 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:07.884 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:07.884 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:07.884 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.884 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.884 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:07.885 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.885 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.885 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:07.886 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:07.892 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:07.892 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:07.893 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:07.894 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:07.894 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:07.894 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.895 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.895 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:07.896 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.896 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.896 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:07.897 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:07.903 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:07.903 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:07.904 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:07.904 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:07.904 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:07.904 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.904 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.904 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:07.905 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.905 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.905 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:07.905 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:07.911 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:07.911 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:07.912 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:07.912 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:07.912 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:07.912 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.913 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.913 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:07.913 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.913 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.913 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:07.914 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:07.918 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:07.918 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:07.919 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:07.919 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:07.919 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:07.919 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.920 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.920 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:07.920 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.920 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.921 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:07.921 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:07.927 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:07.928 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:07.928 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:07.928 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:07.929 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:07.929 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.929 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.930 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:07.930 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.930 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.930 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:07.931 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:07.937 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:07.937 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:07.937 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:07.938 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:07.938 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:07.938 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.938 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.939 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:07.939 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.939 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.939 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:07.940 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:07.946 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:07.946 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:07.946 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:07.947 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:07.947 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:07.947 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.947 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.947 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:07.948 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.948 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.948 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:07.948 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:07.954 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:07.954 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:07.954 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:07.954 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:07.955 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:07.955 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.955 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.955 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:07.956 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.956 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.956 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:07.956 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:07.962 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:07.962 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:07.962 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:07.963 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:07.963 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:07.963 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.963 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.963 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:07.964 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.964 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.964 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:07.964 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:07.970 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:07.970 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:07.970 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:07.971 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:07.971 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:07.971 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.971 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.971 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:07.972 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.972 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.972 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:07.972 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:07.978 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:07.978 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:07.978 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:07.978 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:07.979 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:07.979 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.979 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.979 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:07.979 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.980 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.980 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:07.980 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:07.986 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:07.986 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:07.986 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:07.986 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:07.987 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:07.987 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.987 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.987 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:07.987 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.987 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.988 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:07.988 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:07.994 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:07.994 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:07.994 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:07.995 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:07.995 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:07.995 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.996 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.996 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:07.996 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.996 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:07.997 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:07.997 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.003 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.003 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.003 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.004 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.004 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.004 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.004 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.005 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.005 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.005 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.006 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.006 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.011 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.011 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.011 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.012 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.012 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.012 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.012 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.012 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.013 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.013 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.013 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.014 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.020 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.020 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.020 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.021 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.021 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.021 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.021 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.022 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.022 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.022 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.022 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.023 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.029 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.029 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.029 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.029 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.030 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.030 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.030 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.030 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.030 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.030 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.031 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.031 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.037 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.037 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.037 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.038 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.038 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.038 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.039 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.039 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.039 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.039 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.040 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.040 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.046 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 08:10:08.046 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-04 08:10:08.048 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 08:10:08.048 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 08:10:08.059 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 08:10:08.060 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 08:10:08.060 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 08:10:08.062 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.063 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.063 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.063 [Information] VocomService: Using generic data transfer
2025-06-04 08:10:08.065 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 08:10:08.066 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 08:10:08.066 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.066 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.067 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.068 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 08:10:08.068 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.069 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 08:10:08.069 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 08:10:08.071 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 08:10:08.071 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 08:10:08.082 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 08:10:08.083 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 08:10:08.083 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 08:10:08.094 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 08:10:08.105 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 08:10:08.116 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 08:10:08.127 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 08:10:08.138 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 08:10:08.140 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 08:10:08.140 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 08:10:08.150 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 08:10:08.151 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 08:10:08.151 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 08:10:08.162 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 08:10:08.173 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 08:10:08.184 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 08:10:08.195 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 08:10:08.206 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 08:10:08.217 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 08:10:08.219 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 08:10:08.219 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 08:10:08.230 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 08:10:08.231 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 08:10:08.231 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 08:10:08.231 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 08:10:08.232 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 08:10:08.232 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 08:10:08.232 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 08:10:08.232 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 08:10:08.232 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 08:10:08.233 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 08:10:08.233 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 08:10:08.233 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 08:10:08.233 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 08:10:08.233 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 08:10:08.233 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 08:10:08.234 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 08:10:08.234 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 08:10:08.334 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 08:10:08.334 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 08:10:08.336 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 08:10:08.337 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:08.338 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 08:10:08.338 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 08:10:08.338 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:08.338 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 08:10:08.338 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 08:10:08.339 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:08.339 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 08:10:08.339 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 08:10:08.339 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:08.339 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 08:10:08.340 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 08:10:08.340 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-06-04 08:10:08.342 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-06-04 08:10:08.343 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-06-04 08:10:08.346 [Information] BackupService: Initializing backup service
2025-06-04 08:10:08.347 [Information] BackupService: Backup service initialized successfully
2025-06-04 08:10:08.347 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-06-04 08:10:08.347 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-06-04 08:10:08.349 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-06-04 08:10:08.393 [Information] BackupService: Compressing backup data
2025-06-04 08:10:08.398 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (449 bytes)
2025-06-04 08:10:08.399 [Information] BackupServiceFactory: Created template for category: Production
2025-06-04 08:10:08.399 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-06-04 08:10:08.400 [Information] BackupService: Compressing backup data
2025-06-04 08:10:08.402 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-06-04 08:10:08.402 [Information] BackupServiceFactory: Created template for category: Development
2025-06-04 08:10:08.403 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-06-04 08:10:08.403 [Information] BackupService: Compressing backup data
2025-06-04 08:10:08.404 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (447 bytes)
2025-06-04 08:10:08.404 [Information] BackupServiceFactory: Created template for category: Testing
2025-06-04 08:10:08.405 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-06-04 08:10:08.405 [Information] BackupService: Compressing backup data
2025-06-04 08:10:08.406 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-06-04 08:10:08.406 [Information] BackupServiceFactory: Created template for category: Archived
2025-06-04 08:10:08.407 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-06-04 08:10:08.407 [Information] BackupService: Compressing backup data
2025-06-04 08:10:08.408 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (443 bytes)
2025-06-04 08:10:08.408 [Information] BackupServiceFactory: Created template for category: Critical
2025-06-04 08:10:08.408 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-06-04 08:10:08.409 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-06-04 08:10:08.410 [Information] BackupService: Compressing backup data
2025-06-04 08:10:08.411 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (512 bytes)
2025-06-04 08:10:08.411 [Information] BackupServiceFactory: Created template with predefined tags
2025-06-04 08:10:08.412 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-06-04 08:10:08.413 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-06-04 08:10:08.416 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 08:10:08.418 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 08:10:08.456 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 08:10:08.457 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 08:10:08.457 [Information] BackupSchedulerService: Starting backup scheduler
2025-06-04 08:10:08.458 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-06-04 08:10:08.458 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-06-04 08:10:08.459 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-06-04 08:10:08.459 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-06-04 08:10:08.461 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-06-04 08:10:08.461 [Information] App: Flash operation monitor service initialized successfully
2025-06-04 08:10:08.467 [Information] LicensingService: Initializing licensing service
2025-06-04 08:10:08.512 [Error] LicensingService: Error loading license information Exception: Padding is invalid and cannot be removed. StackTrace:    at System.Security.Cryptography.SymmetricPadding.GetPaddingLength(ReadOnlySpan`1 block, PaddingMode paddingMode, Int32 blockSize)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(ReadOnlySpan`1 inputBuffer, Span`1 outputBuffer)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.UniversalCryptoTransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.CryptoStream.ReadAsyncCore(Memory`1 buffer, CancellationToken cancellationToken, Boolean useAsync)
   at System.Security.Cryptography.CryptoStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at System.IO.StreamReader.ReadBuffer()
   at System.IO.StreamReader.ReadToEnd()
   at VolvoFlashWR.Core.Services.LicensingService.DecryptData(Byte[] encryptedData) in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 424
   at VolvoFlashWR.Core.Services.LicensingService.LoadLicenseInfoAsync() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 333
2025-06-04 08:10:08.514 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-06-04 08:10:08.514 [Information] App: Licensing service initialized successfully
2025-06-04 08:10:08.514 [Information] App: License status: Trial
2025-06-04 08:10:08.515 [Information] App: Trial period: 30 days remaining
2025-06-04 08:10:08.515 [Information] BackupSchedulerService: Getting all backup schedules
2025-06-04 08:10:08.671 [Information] VocomService: Initializing Vocom service
2025-06-04 08:10:08.671 [Information] VocomService: Checking if PTT application is running
2025-06-04 08:10:08.680 [Information] VocomService: PTT application is not running
2025-06-04 08:10:08.681 [Information] VocomService: Vocom service initialized successfully
2025-06-04 08:10:08.794 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 08:10:08.795 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 08:10:08.795 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 08:10:08.795 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 08:10:08.796 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 08:10:08.796 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 08:10:08.797 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 08:10:08.820 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 08:10:08.821 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 08:10:08.821 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 08:10:08.832 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 08:10:08.833 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 08:10:08.833 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 08:10:08.833 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 08:10:08.834 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 08:10:08.834 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 08:10:08.834 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 08:10:08.835 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 08:10:08.835 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 08:10:08.835 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 08:10:08.835 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 08:10:08.836 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 08:10:08.836 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 08:10:08.836 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 08:10:08.836 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 08:10:08.836 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 08:10:08.837 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 08:10:08.837 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 08:10:08.837 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.838 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.838 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.838 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.838 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.839 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.839 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.839 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.840 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.840 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-04 08:10:08.840 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-04 08:10:08.841 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 08:10:08.841 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 08:10:08.841 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.841 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.841 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.842 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.842 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.842 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.842 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.842 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.843 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.843 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.843 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.843 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.848 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.849 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.849 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.849 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.849 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.849 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.850 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.850 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.850 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.850 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.851 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.851 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.856 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.857 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.857 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.857 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.857 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.857 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.857 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.858 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.858 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.858 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.858 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.859 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.864 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.865 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.865 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.865 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.865 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.865 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.866 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.866 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.866 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.866 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.867 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.867 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.872 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.873 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.873 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.873 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.873 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.873 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.874 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.874 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.874 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.874 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.875 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.875 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.881 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.882 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.882 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.882 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.882 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.883 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.883 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.883 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.884 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.884 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.884 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.884 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.890 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.891 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.891 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.891 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.891 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.892 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.892 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.892 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.892 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.893 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.893 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.894 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.899 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.900 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.900 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.900 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.900 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.901 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.901 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.901 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.901 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.902 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.902 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.902 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.908 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.909 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.909 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.909 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.909 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.910 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.910 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.910 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.911 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.911 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.911 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.912 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.918 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.919 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.919 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.919 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.919 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.919 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.920 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.920 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.920 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.920 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.920 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.921 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.926 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.927 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.927 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.928 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.928 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.928 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.929 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.929 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.929 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.930 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.930 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.930 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.936 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.937 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.937 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.937 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.937 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.937 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.938 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.938 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.938 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.939 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.939 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.939 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.945 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.946 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.946 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.946 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.946 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.946 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.947 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.947 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.947 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.947 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.947 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.948 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.954 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.954 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.955 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.955 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.955 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.955 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.956 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.956 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.956 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.956 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.957 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.957 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.963 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.963 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.964 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.964 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.964 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.964 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.964 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.965 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.965 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.965 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.965 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.966 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.972 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.972 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.973 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.973 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.973 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.973 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.974 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.974 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.974 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.974 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.974 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.975 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.980 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.981 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.981 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.981 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.981 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.982 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.982 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.982 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.983 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.983 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.983 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.983 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.989 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.990 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:08.990 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:08.990 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:08.991 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:08.991 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.991 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.991 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:08.992 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.992 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:08.992 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:08.993 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:08.999 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:08.999 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:09.000 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:09.000 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:09.000 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:09.000 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:09.001 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:09.001 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:09.001 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:09.002 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:09.002 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:09.002 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:09.008 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:09.009 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:09.009 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:09.009 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:09.010 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:09.010 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:09.010 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:09.010 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:09.010 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:09.011 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:09.011 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:09.011 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:09.017 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 08:10:09.018 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-04 08:10:09.018 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 08:10:09.018 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 08:10:09.029 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 08:10:09.029 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 08:10:09.030 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 08:10:09.030 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:09.030 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:09.030 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:09.030 [Information] VocomService: Using generic data transfer
2025-06-04 08:10:09.031 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 08:10:09.031 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 08:10:09.031 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:09.032 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:09.032 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:09.032 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 08:10:09.032 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:09.033 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 08:10:09.033 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 08:10:09.033 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 08:10:09.033 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 08:10:09.044 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 08:10:09.044 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 08:10:09.045 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 08:10:09.056 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 08:10:09.067 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 08:10:09.077 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 08:10:09.088 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 08:10:09.099 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 08:10:09.100 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 08:10:09.100 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 08:10:09.111 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 08:10:09.112 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 08:10:09.112 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 08:10:09.122 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 08:10:09.133 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 08:10:09.144 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 08:10:09.155 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 08:10:09.166 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 08:10:09.177 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 08:10:09.178 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 08:10:09.178 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 08:10:09.189 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 08:10:09.189 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 08:10:09.189 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 08:10:09.189 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 08:10:09.190 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 08:10:09.190 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 08:10:09.190 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 08:10:09.190 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 08:10:09.190 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 08:10:09.191 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 08:10:09.191 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 08:10:09.191 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 08:10:09.191 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 08:10:09.191 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 08:10:09.191 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 08:10:09.192 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 08:10:09.192 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 08:10:09.292 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 08:10:09.292 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 08:10:09.293 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 08:10:09.294 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:09.294 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 08:10:09.294 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 08:10:09.294 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:09.294 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 08:10:09.294 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 08:10:09.295 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:09.295 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 08:10:09.295 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 08:10:09.295 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:09.295 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 08:10:09.296 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 08:10:09.347 [Information] BackupService: Initializing backup service
2025-06-04 08:10:09.347 [Information] BackupService: Backup service initialized successfully
2025-06-04 08:10:09.398 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 08:10:09.398 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 08:10:09.399 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 08:10:09.399 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 08:10:09.450 [Information] BackupService: Getting predefined backup categories
2025-06-04 08:10:09.501 [Information] MainViewModel: Services initialized successfully
2025-06-04 08:10:09.503 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 08:10:09.504 [Information] VocomService: Scanning for Vocom devices
2025-06-04 08:10:09.505 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 08:10:09.505 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 08:10:09.506 [Information] VocomService: Found 2 Vocom devices
2025-06-04 08:10:09.507 [Information] MainViewModel: Found 2 Vocom device(s)
2025-06-04 08:10:11.788 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 08:10:11.790 [Information] VocomService: Scanning for Vocom devices
2025-06-04 08:10:11.790 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 08:10:11.790 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 08:10:11.792 [Information] VocomService: Found 2 Vocom devices
2025-06-04 08:10:11.793 [Information] MainViewModel: Found 2 Vocom device(s)
2025-06-04 08:10:21.198 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 08:10:21.200 [Information] VocomService: Scanning for Vocom devices
2025-06-04 08:10:21.200 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 08:10:21.200 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 08:10:21.201 [Information] VocomService: Found 2 Vocom devices
2025-06-04 08:10:21.201 [Information] MainViewModel: Found 2 Vocom device(s)
