Log started at 6/4/2025 8:10:43 AM
2025-06-04 08:10:43.040 [Information] LoggingService: Logging service initialized
2025-06-04 08:10:43.051 [Information] AppConfigurationService: Initializing configuration service
2025-06-04 08:10:43.053 [Information] AppConfigurationService: Created configuration directory: C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Config
2025-06-04 08:10:43.054 [Information] AppConfigurationService: Configuration file not found, creating default
2025-06-04 08:10:43.060 [Warning] AppConfigurationService: Configuration service not initialized
2025-06-04 08:10:43.061 [Information] AppConfigurationService: Default configuration created
2025-06-04 08:10:43.061 [Information] AppConfigurationService: Configuration service initialized successfully
2025-06-04 08:10:43.061 [Information] App: Configuration service initialized successfully
2025-06-04 08:10:43.062 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-06-04 08:10:43.063 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: ''
2025-06-04 08:10:43.063 [Information] App: Environment variable exists: False, not 'false': True
2025-06-04 08:10:43.063 [Information] App: Final useDummyImplementations value: False
2025-06-04 08:10:43.063 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: ''
2025-06-04 08:10:43.064 [Information] App: usePatchedImplementation flag is: False
2025-06-04 08:10:43.064 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: ''
2025-06-04 08:10:43.065 [Information] App: APCI_LIBRARY_PATH environment variable is set to: ''
2025-06-04 08:10:43.065 [Information] App: VERBOSE_LOGGING environment variable is set to: ''
2025-06-04 08:10:43.066 [Information] App: verboseLogging flag is: False
2025-06-04 08:10:43.069 [Information] App: Verifying real hardware requirements...
2025-06-04 08:10:43.070 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-06-04 08:10:43.070 [Information] App: ✓ Found critical library: apci.dll
2025-06-04 08:10:43.070 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-06-04 08:10:43.071 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-06-04 08:10:43.071 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 08:10:43.071 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-06-04 08:10:43.072 [Information] App: ✓ Found Vocom driver config: C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Vocom\config.json
2025-06-04 08:10:43.072 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-06-04 08:10:43.088 [Information] App: Creating standard VocomServiceFactory instance
2025-06-04 08:10:43.089 [Information] App: Successfully created standard VocomServiceFactory instance
2025-06-04 08:10:43.089 [Information] App: Using VolvoFlashWR.Communication.Vocom.VocomServiceFactory Vocom service factory
2025-06-04 08:10:43.089 [Information] App: Checking if PTT application is running before creating Vocom service
2025-06-04 08:10:43.127 [Information] App: Creating Vocom service (attempt 1/3)
2025-06-04 08:10:43.129 [Information] VocomServiceFactory: Creating Vocom service with default settings
2025-06-04 08:10:43.130 [Information] VocomServiceFactory: Phoenix Vocom adapter not enabled, skipping
2025-06-04 08:10:43.130 [Information] VocomServiceFactory: Phoenix adapter initialization failed, attempting to create standard Vocom driver
2025-06-04 08:10:43.133 [Information] VocomDriver: Initializing Vocom driver
2025-06-04 08:10:43.136 [Information] VocomNativeInterop: Initializing Vocom driver
2025-06-04 08:10:43.141 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-06-04 08:10:43.141 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 08:10:43.142 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 08:10:43.142 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 08:10:43.143 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-06-04 08:10:43.145 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr120.dll
2025-06-04 08:10:43.146 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcp120.dll
2025-06-04 08:10:43.147 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-06-04 08:10:43.149 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcp140.dll
2025-06-04 08:10:43.151 [Warning] WUDFPumaDependencyResolver: Could not load dependency: vcruntime140.dll
2025-06-04 08:10:43.153 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-06-04 08:10:43.154 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-06-04 08:10:43.155 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-06-04 08:10:43.156 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-06-04 08:10:43.156 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-06-04 08:10:43.157 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-06-04 08:10:43.157 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-06-04 08:10:43.157 [Information] VocomDriver: Vocom driver initialized successfully
2025-06-04 08:10:43.159 [Information] VocomService: Initializing Vocom service with dependencies
2025-06-04 08:10:43.160 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-06-04 08:10:43.161 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-06-04 08:10:43.161 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-06-04 08:10:43.213 [Information] WiFiCommunicationService: WiFi is available
2025-06-04 08:10:43.216 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-06-04 08:10:43.219 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-06-04 08:10:43.220 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-06-04 08:10:43.221 [Information] BluetoothCommunicationService: Bluetooth is available
2025-06-04 08:10:43.222 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-06-04 08:10:43.222 [Information] VocomService: Initializing Vocom service
2025-06-04 08:10:43.224 [Information] VocomService: Checking if PTT application is running
2025-06-04 08:10:43.240 [Information] VocomService: PTT application is not running
2025-06-04 08:10:43.242 [Information] VocomService: Vocom service initialized successfully
2025-06-04 08:10:43.243 [Information] VocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-06-04 08:10:43.243 [Information] App: Initializing Vocom service
2025-06-04 08:10:43.243 [Information] VocomService: Initializing Vocom service
2025-06-04 08:10:43.243 [Information] VocomService: Checking if PTT application is running
2025-06-04 08:10:43.260 [Information] VocomService: PTT application is not running
2025-06-04 08:10:43.261 [Information] VocomService: Vocom service initialized successfully
2025-06-04 08:10:43.262 [Information] VocomService: Scanning for Vocom devices
2025-06-04 08:10:43.268 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 08:10:43.292 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 08:10:43.295 [Information] VocomService: Found 2 Vocom devices
2025-06-04 08:10:43.295 [Information] App: Found 2 Vocom devices, attempting to connect to the first one
2025-06-04 08:10:43.298 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 08:10:43.298 [Information] VocomService: Checking if PTT application is running
2025-06-04 08:10:43.310 [Information] VocomService: PTT application is not running
2025-06-04 08:10:43.312 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 08:10:43.313 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-06-04 08:10:44.117 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 08:10:44.118 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 08:10:44.118 [Information] App: Connected to Vocom device Vocom - 88890300 (Bluetooth)
2025-06-04 08:10:44.120 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-06-04 08:10:44.122 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:44.122 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-06-04 08:10:44.124 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 08:10:44.126 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 08:10:44.126 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 08:10:44.128 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 08:10:44.130 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 08:10:44.138 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 08:10:44.139 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 08:10:44.141 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 08:10:44.145 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 08:10:44.148 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 08:10:44.160 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 08:10:44.161 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 08:10:44.161 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 08:10:44.162 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 08:10:44.162 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 08:10:44.162 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 08:10:44.162 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 08:10:44.162 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 08:10:44.163 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 08:10:44.165 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 08:10:44.165 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 08:10:44.165 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 08:10:44.166 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 08:10:44.166 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 08:10:44.166 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 08:10:44.166 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 08:10:44.167 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 08:10:44.169 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 08:10:44.170 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.171 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.171 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.171 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.172 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.173 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.175 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.176 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.177 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.178 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-04 08:10:44.179 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-04 08:10:44.180 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 08:10:44.183 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 08:10:44.184 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.185 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.185 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.185 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.186 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.186 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.186 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.186 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.186 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.187 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.187 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.188 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.195 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.195 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.195 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.196 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.196 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.196 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.196 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.196 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.197 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.197 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.197 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.198 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.204 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.204 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.204 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.204 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.205 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.205 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.205 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.205 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.205 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.206 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.206 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.206 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.213 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.213 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.214 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.214 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.214 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.214 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.214 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.215 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.215 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.215 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.215 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.215 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.222 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.222 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.222 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.222 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.222 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.223 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.223 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.223 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.223 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.223 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.224 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.224 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.230 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.230 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.230 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.231 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.231 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.231 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.231 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.231 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.232 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.232 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.232 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.232 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.239 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.239 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.239 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.240 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.240 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.240 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.240 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.240 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.241 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.242 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.242 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.242 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.248 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.249 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.249 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.249 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.249 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.250 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.250 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.250 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.250 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.250 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.251 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.251 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.256 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.257 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.257 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.258 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.258 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.258 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.259 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.259 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.259 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.259 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.260 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.260 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.265 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.266 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.266 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.266 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.266 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.267 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.267 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.267 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.267 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.267 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.267 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.268 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.273 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.274 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.274 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.274 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.274 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.275 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.275 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.275 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.275 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.275 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.276 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.276 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.281 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.282 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.282 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.282 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.282 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.283 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.283 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.283 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.283 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.284 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.284 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.284 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.289 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.290 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.290 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.290 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.290 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.291 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.291 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.291 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.291 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.291 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.292 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.292 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.297 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.298 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.298 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.298 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.299 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.299 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.299 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.300 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.300 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.300 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.300 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.300 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.306 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.307 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.307 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.307 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.308 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.308 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.308 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.308 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.309 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.309 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.309 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.309 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.315 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.315 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.315 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.316 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.316 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.316 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.316 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.316 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.317 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.317 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.317 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.317 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.323 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.323 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.323 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.324 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.324 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.324 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.324 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.324 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.325 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.325 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.325 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.325 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.331 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.331 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.331 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.332 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.332 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.332 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.332 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.332 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.332 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.333 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.333 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.333 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.339 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.339 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.339 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.340 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.340 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.340 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.340 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.340 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.341 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.341 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.341 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.341 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.347 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:44.347 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.347 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.348 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.348 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:44.348 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.349 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.349 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.349 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.349 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.350 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.350 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:44.356 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 08:10:44.357 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-04 08:10:44.359 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 08:10:44.359 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 08:10:44.370 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 08:10:44.371 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 08:10:44.371 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 08:10:44.373 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:44.373 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:44.373 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:44.373 [Information] VocomService: Using generic data transfer
2025-06-04 08:10:44.375 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 08:10:44.375 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 08:10:44.375 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.376 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:44.376 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:44.377 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 08:10:44.377 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:44.378 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 08:10:44.379 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 08:10:44.380 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 08:10:44.380 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 08:10:44.392 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 08:10:44.393 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 08:10:44.393 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 08:10:44.404 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 08:10:44.414 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 08:10:44.425 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 08:10:44.435 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 08:10:44.446 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 08:10:44.449 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 08:10:44.449 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 08:10:44.460 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 08:10:44.461 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 08:10:44.462 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 08:10:44.473 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 08:10:44.484 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 08:10:44.495 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 08:10:44.506 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 08:10:44.517 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 08:10:44.528 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 08:10:44.530 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 08:10:44.531 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 08:10:44.542 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 08:10:44.543 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 08:10:44.543 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 08:10:44.544 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 08:10:44.544 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 08:10:44.544 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 08:10:44.544 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 08:10:44.544 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 08:10:44.544 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 08:10:44.545 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 08:10:44.545 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 08:10:44.545 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 08:10:44.545 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 08:10:44.545 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 08:10:44.546 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 08:10:44.546 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 08:10:44.546 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 08:10:44.647 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 08:10:44.648 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 08:10:44.651 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 08:10:44.652 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:44.652 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 08:10:44.653 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 08:10:44.653 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:44.653 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 08:10:44.654 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 08:10:44.654 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:44.654 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 08:10:44.655 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 08:10:44.655 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:44.655 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 08:10:44.656 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 08:10:44.656 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-06-04 08:10:44.659 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-06-04 08:10:44.661 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-06-04 08:10:44.665 [Information] BackupService: Initializing backup service
2025-06-04 08:10:44.665 [Information] BackupService: Backup service initialized successfully
2025-06-04 08:10:44.666 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-06-04 08:10:44.666 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-06-04 08:10:44.668 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-06-04 08:10:44.713 [Information] BackupService: Compressing backup data
2025-06-04 08:10:44.719 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (449 bytes)
2025-06-04 08:10:44.720 [Information] BackupServiceFactory: Created template for category: Production
2025-06-04 08:10:44.721 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-06-04 08:10:44.721 [Information] BackupService: Compressing backup data
2025-06-04 08:10:44.722 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (448 bytes)
2025-06-04 08:10:44.722 [Information] BackupServiceFactory: Created template for category: Development
2025-06-04 08:10:44.722 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-06-04 08:10:44.723 [Information] BackupService: Compressing backup data
2025-06-04 08:10:44.723 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (444 bytes)
2025-06-04 08:10:44.723 [Information] BackupServiceFactory: Created template for category: Testing
2025-06-04 08:10:44.724 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-06-04 08:10:44.724 [Information] BackupService: Compressing backup data
2025-06-04 08:10:44.725 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (449 bytes)
2025-06-04 08:10:44.725 [Information] BackupServiceFactory: Created template for category: Archived
2025-06-04 08:10:44.725 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-06-04 08:10:44.725 [Information] BackupService: Compressing backup data
2025-06-04 08:10:44.726 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (447 bytes)
2025-06-04 08:10:44.726 [Information] BackupServiceFactory: Created template for category: Critical
2025-06-04 08:10:44.727 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-06-04 08:10:44.727 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-06-04 08:10:44.728 [Information] BackupService: Compressing backup data
2025-06-04 08:10:44.729 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (512 bytes)
2025-06-04 08:10:44.729 [Information] BackupServiceFactory: Created template with predefined tags
2025-06-04 08:10:44.729 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-06-04 08:10:44.731 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-06-04 08:10:44.734 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 08:10:44.736 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 08:10:44.779 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 08:10:44.780 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 08:10:44.781 [Information] BackupSchedulerService: Starting backup scheduler
2025-06-04 08:10:44.781 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-06-04 08:10:44.781 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-06-04 08:10:44.782 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-06-04 08:10:44.782 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-06-04 08:10:44.785 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-06-04 08:10:44.785 [Information] App: Flash operation monitor service initialized successfully
2025-06-04 08:10:44.791 [Information] LicensingService: Initializing licensing service
2025-06-04 08:10:44.832 [Error] LicensingService: Error loading license information Exception: Padding is invalid and cannot be removed. StackTrace:    at System.Security.Cryptography.SymmetricPadding.GetPaddingLength(ReadOnlySpan`1 block, PaddingMode paddingMode, Int32 blockSize)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(ReadOnlySpan`1 inputBuffer, Span`1 outputBuffer)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.UniversalCryptoTransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.CryptoStream.ReadAsyncCore(Memory`1 buffer, CancellationToken cancellationToken, Boolean useAsync)
   at System.Security.Cryptography.CryptoStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at System.IO.StreamReader.ReadBuffer()
   at System.IO.StreamReader.ReadToEnd()
   at VolvoFlashWR.Core.Services.LicensingService.DecryptData(Byte[] encryptedData) in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 424
   at VolvoFlashWR.Core.Services.LicensingService.LoadLicenseInfoAsync() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 333
2025-06-04 08:10:44.833 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-06-04 08:10:44.834 [Information] App: Licensing service initialized successfully
2025-06-04 08:10:44.834 [Information] App: License status: Trial
2025-06-04 08:10:44.834 [Information] App: Trial period: 30 days remaining
2025-06-04 08:10:44.835 [Information] BackupSchedulerService: Getting all backup schedules
2025-06-04 08:10:44.991 [Information] VocomService: Initializing Vocom service
2025-06-04 08:10:44.991 [Information] VocomService: Checking if PTT application is running
2025-06-04 08:10:44.999 [Information] VocomService: PTT application is not running
2025-06-04 08:10:45.000 [Information] VocomService: Vocom service initialized successfully
2025-06-04 08:10:45.051 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 08:10:45.052 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 08:10:45.052 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 08:10:45.052 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 08:10:45.053 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 08:10:45.054 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 08:10:45.054 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 08:10:45.054 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 08:10:45.055 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 08:10:45.055 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 08:10:45.066 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 08:10:45.066 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 08:10:45.067 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 08:10:45.067 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 08:10:45.067 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 08:10:45.067 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 08:10:45.067 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 08:10:45.067 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 08:10:45.068 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 08:10:45.068 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 08:10:45.068 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 08:10:45.068 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 08:10:45.068 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 08:10:45.069 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 08:10:45.069 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 08:10:45.069 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 08:10:45.069 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 08:10:45.069 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 08:10:45.070 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.070 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.070 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.070 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.070 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.070 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.071 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.071 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.071 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.072 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-04 08:10:45.072 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-04 08:10:45.072 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 08:10:45.072 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 08:10:45.072 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.073 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.073 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.073 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.073 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.073 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.074 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.074 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.074 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.075 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.075 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.075 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.081 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.081 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.082 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.082 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.082 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.082 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.082 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.082 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.083 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.083 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.083 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.083 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.089 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.089 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.090 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.090 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.090 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.091 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.091 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.091 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.091 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.092 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.092 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.092 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.098 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.098 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.098 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.099 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.099 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.099 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.099 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.100 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.100 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.100 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.101 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.101 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.107 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.107 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.108 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.108 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.108 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.108 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.109 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.109 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.109 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.109 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.110 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.110 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.116 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.116 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.117 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.117 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.117 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.117 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.118 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.118 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.118 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.119 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.119 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.119 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.125 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.125 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.125 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.126 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.126 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.126 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.126 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.127 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.127 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.127 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.128 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.128 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.134 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.134 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.134 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.135 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.135 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.135 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.136 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.136 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.136 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.137 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.137 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.137 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.144 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.144 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.144 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.145 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.145 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.145 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.145 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.146 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.146 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.146 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.147 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.147 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.154 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.154 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.154 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.155 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.155 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.155 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.155 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.156 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.156 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.156 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.157 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.157 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.162 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.162 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.163 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.163 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.163 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.164 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.164 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.164 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.165 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.165 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.165 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.166 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.172 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.172 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.172 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.173 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.173 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.173 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.173 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.174 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.174 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.174 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.175 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.175 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.181 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.181 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.181 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.181 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.182 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.182 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.182 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.182 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.182 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.183 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.183 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.183 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.189 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.189 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.189 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.190 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.190 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.190 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.191 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.191 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.191 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.192 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.192 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.192 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.199 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.199 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.199 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.199 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.200 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.200 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.200 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.200 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.201 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.201 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.201 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.201 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.208 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.208 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.208 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.209 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.209 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.209 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.209 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.210 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.210 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.210 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.210 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.211 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.217 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.217 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.217 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.218 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.218 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.218 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.218 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.219 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.219 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.219 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.220 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.220 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.225 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.226 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.226 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.226 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.226 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.226 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.227 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.227 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.227 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.227 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.228 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.228 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.233 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.234 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.234 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.234 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.235 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.235 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.235 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.236 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.236 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.236 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.236 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.237 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.242 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 08:10:45.243 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.243 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.243 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.244 [Information] VocomService: Detected CAN protocol request
2025-06-04 08:10:45.244 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.244 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.244 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.244 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.245 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.245 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.245 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 08:10:45.251 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 08:10:45.252 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-04 08:10:45.252 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 08:10:45.253 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 08:10:45.263 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 08:10:45.264 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 08:10:45.264 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 08:10:45.265 [Information] VocomService: Sending data and waiting for response
2025-06-04 08:10:45.265 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 08:10:45.265 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 08:10:45.265 [Information] VocomService: Using generic data transfer
2025-06-04 08:10:45.265 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 08:10:45.265 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 08:10:45.266 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.266 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 08:10:45.266 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 08:10:45.267 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 08:10:45.267 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 08:10:45.267 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 08:10:45.267 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 08:10:45.268 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 08:10:45.268 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 08:10:45.278 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 08:10:45.279 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 08:10:45.279 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 08:10:45.290 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 08:10:45.301 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 08:10:45.312 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 08:10:45.323 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 08:10:45.334 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 08:10:45.335 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 08:10:45.335 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 08:10:45.346 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 08:10:45.347 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 08:10:45.347 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 08:10:45.358 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 08:10:45.369 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 08:10:45.380 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 08:10:45.391 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 08:10:45.402 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 08:10:45.413 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 08:10:45.414 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 08:10:45.414 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 08:10:45.425 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 08:10:45.425 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 08:10:45.426 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 08:10:45.426 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 08:10:45.426 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 08:10:45.426 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 08:10:45.426 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 08:10:45.427 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 08:10:45.427 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 08:10:45.427 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 08:10:45.427 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 08:10:45.427 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 08:10:45.427 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 08:10:45.428 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 08:10:45.428 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 08:10:45.428 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 08:10:45.428 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 08:10:45.528 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 08:10:45.529 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 08:10:45.529 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 08:10:45.530 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:45.530 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 08:10:45.530 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 08:10:45.531 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:45.531 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 08:10:45.531 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 08:10:45.532 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:45.532 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 08:10:45.532 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 08:10:45.532 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 08:10:45.533 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 08:10:45.533 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 08:10:45.584 [Information] BackupService: Initializing backup service
2025-06-04 08:10:45.585 [Information] BackupService: Backup service initialized successfully
2025-06-04 08:10:45.635 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 08:10:45.636 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 08:10:45.636 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048 (2)\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 08:10:45.637 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 08:10:45.689 [Information] BackupService: Getting predefined backup categories
2025-06-04 08:10:45.740 [Information] MainViewModel: Services initialized successfully
2025-06-04 08:10:45.742 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 08:10:45.743 [Information] VocomService: Scanning for Vocom devices
2025-06-04 08:10:45.743 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 08:10:45.744 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 08:10:45.745 [Information] VocomService: Found 2 Vocom devices
2025-06-04 08:10:45.745 [Information] MainViewModel: Found 2 Vocom device(s)
2025-06-04 08:10:50.156 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 08:10:50.157 [Information] VocomService: Scanning for Vocom devices
2025-06-04 08:10:50.158 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 08:10:50.158 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 08:10:50.159 [Information] VocomService: Found 2 Vocom devices
2025-06-04 08:10:50.160 [Information] MainViewModel: Found 2 Vocom device(s)
