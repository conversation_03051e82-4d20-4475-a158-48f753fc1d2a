@echo off
title VolvoFlashWR - Real Hardware Mode
echo === VolvoFlashWR Real Hardware Mode ===
echo.

echo Checking for Vocom adapter...
echo.

REM Check if Vocom driver is installed
if not exist "C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll" (
    echo ! WARNING: Vocom driver not found in system!
    echo Please install the Vocom driver first.
    echo.
)

REM Check if libraries are present
if not exist "Libraries\WUDFPuma.dll" (
    echo X Critical library missing: WUDFPuma.dll
    echo Please run Configure_Real_Hardware_Libraries.ps1 first.
    pause
    exit /b 1
)

echo + Libraries check passed
echo.

echo Starting VolvoFlashWR in Real Hardware Mode...
echo.

REM Set environment variables for library loading and enable Phoenix adapter
set PATH=%PATH%;%CD%\Libraries;%CD%\Drivers\Vocom;C:\Program Files (x86)\88890020 Adapter\UMDF
set USE_DUMMY_IMPLEMENTATIONS=false
set USE_PATCHED_IMPLEMENTATION=true
set VERBOSE_LOGGING=true
set PHOENIX_VOCOM_ENABLED=true
set PHOENIX_VOCOM_ADAPTER=true
set APCI_LIBRARY_PATH=%CD%\Libraries
set VOCOM_DRIVER_MODE=REAL_HARDWARE

REM Export environment variables to ensure they persist
setx USE_DUMMY_IMPLEMENTATIONS false /M >nul 2>&1
setx USE_PATCHED_IMPLEMENTATION true /M >nul 2>&1
setx PHOENIX_VOCOM_ENABLED true /M >nul 2>&1
setx VERBOSE_LOGGING true /M >nul 2>&1

echo Environment variables set for real hardware mode:
echo - USE_DUMMY_IMPLEMENTATIONS=%USE_DUMMY_IMPLEMENTATIONS%
echo - USE_PATCHED_IMPLEMENTATION=%USE_PATCHED_IMPLEMENTATION%
echo - PHOENIX_VOCOM_ENABLED=%PHOENIX_VOCOM_ENABLED%
echo - VOCOM_DRIVER_MODE=%VOCOM_DRIVER_MODE%
echo.

REM Start the application (64-bit version for .NET 8.0 compatibility)
if exist "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\VolvoFlashWR.Launcher.exe" (
    echo Starting 64-bit application for .NET 8.0 compatibility...
    cd /d "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64"
    start "" "VolvoFlashWR.Launcher.exe" --mode=normal --hardware=real
) else if exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\VolvoFlashWR.Launcher.exe" (
    echo Starting 64-bit debug application...
    cd /d "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64"
    start "" "VolvoFlashWR.Launcher.exe" --mode=normal --hardware=real
) else if exist "VolvoFlashWR_RealHardware_Export\Application\VolvoFlashWR.Launcher.exe" (
    echo Starting exported application...
    cd /d "VolvoFlashWR_RealHardware_Export\Application"
    start "" "VolvoFlashWR.Launcher.exe" --mode=normal --hardware=real
) else (
    echo X VolvoFlashWR.Launcher.exe not found!
    echo Please build the application first using: dotnet build --configuration Release
    pause
    exit /b 1
)

echo Application started. Check the application window for connection status.
echo.
pause
