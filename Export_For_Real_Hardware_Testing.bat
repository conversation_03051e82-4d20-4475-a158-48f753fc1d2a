@echo off
setlocal enabledelayedexpansion

echo ========================================
echo VolvoFlashWR Real Hardware Export Tool
echo ========================================
echo.
echo This script will:
echo 1. Clean all caches and temporary files
echo 2. Build the application in Release mode
echo 3. Export all necessary files for real hardware testing
echo 4. Create a portable package for another laptop
echo.

REM Set export directory
set EXPORT_DIR=VolvoFlashWR_RealHardware_Export
set TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set EXPORT_DIR_FULL=%EXPORT_DIR%_%TIMESTAMP%

echo Creating export directory: %EXPORT_DIR_FULL%
if exist "%EXPORT_DIR_FULL%" rmdir /s /q "%EXPORT_DIR_FULL%"
mkdir "%EXPORT_DIR_FULL%"
mkdir "%EXPORT_DIR_FULL%\Application"
mkdir "%EXPORT_DIR_FULL%\Libraries"
mkdir "%EXPORT_DIR_FULL%\Drivers"
mkdir "%EXPORT_DIR_FULL%\Documentation"
mkdir "%EXPORT_DIR_FULL%\Scripts"

echo.
echo ========================================
echo Step 1: Cleaning Caches and Build Files
echo ========================================

REM Clean all bin and obj directories
echo Cleaning bin and obj directories...
for /d /r . %%d in (bin,obj) do (
    if exist "%%d" (
        echo Deleting: %%d
        rmdir /s /q "%%d" 2>nul
    )
)

REM Clean NuGet cache
echo Cleaning NuGet cache...
dotnet nuget locals all --clear

REM Clean Visual Studio cache
echo Cleaning Visual Studio cache...
if exist "%LOCALAPPDATA%\Microsoft\VisualStudio" (
    for /d %%d in ("%LOCALAPPDATA%\Microsoft\VisualStudio\*") do (
        if exist "%%d\ComponentModelCache" rmdir /s /q "%%d\ComponentModelCache" 2>nul
        if exist "%%d\Extensions" rmdir /s /q "%%d\Extensions" 2>nul
    )
)

REM Clean application-specific cache files
echo Cleaning application cache files...
if exist "Logs" rmdir /s /q "Logs"
if exist "Backups\*.backup" del /q "Backups\*.backup" 2>nul
if exist "VolvoFlashWR.Launcher\bin" rmdir /s /q "VolvoFlashWR.Launcher\bin"
if exist "VolvoFlashWR.UI\bin" rmdir /s /q "VolvoFlashWR.UI\bin"

REM Clean configuration files that might contain cached device info
echo Cleaning cached configuration files...
if exist "VolvoFlashWR.Launcher\Config\app_config.json" del /q "VolvoFlashWR.Launcher\Config\app_config.json"
if exist "VolvoFlashWR.UI\Config\app_config.json" del /q "VolvoFlashWR.UI\Config\app_config.json"

echo Cache cleaning completed!
echo.

echo ========================================
echo Step 2: Building Application (Release Mode)
echo ========================================

echo Building solution in Release mode for x64...
dotnet clean --configuration Release --verbosity minimal
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to clean solution
    pause
    exit /b 1
)

dotnet restore --verbosity minimal
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to restore packages
    pause
    exit /b 1
)

dotnet build --configuration Release --verbosity minimal --no-restore
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to build solution
    pause
    exit /b 1
)

echo Build completed successfully!
echo.

echo ========================================
echo Step 3: Copying Application Files
echo ========================================

REM Copy main application files
echo Copying main application files...
set SOURCE_DIR=VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64
if not exist "%SOURCE_DIR%" (
    echo ERROR: Release build not found at %SOURCE_DIR%
    pause
    exit /b 1
)

xcopy "%SOURCE_DIR%\*" "%EXPORT_DIR_FULL%\Application\" /E /I /Y
echo Application files copied successfully!

echo.
echo ========================================
echo Step 4: Copying Libraries and Dependencies
echo ========================================

REM Copy all libraries from Libraries folder
echo Copying Libraries folder...
xcopy "Libraries\*" "%EXPORT_DIR_FULL%\Libraries\" /E /I /Y

REM Copy system libraries backup
echo Copying system libraries backup...
if exist "Libraries_Backup" (
    xcopy "Libraries_Backup\*" "%EXPORT_DIR_FULL%\Libraries\System\" /E /I /Y
)

echo Libraries copied successfully!

echo.
echo ========================================
echo Step 5: Copying Drivers and Configuration
echo ========================================

REM Copy driver files
echo Copying driver files...
if exist "Drivers" (
    xcopy "Drivers\*" "%EXPORT_DIR_FULL%\Drivers\" /E /I /Y
)

REM Copy MC9S12XEP100 documentation
echo Copying MC9S12XEP100 documentation...
if exist "MC9S12XEP100RMV1-1358561" (
    xcopy "MC9S12XEP100RMV1-1358561\*" "%EXPORT_DIR_FULL%\Documentation\MC9S12XEP100\" /E /I /Y
)

echo Drivers and documentation copied successfully!

echo.
echo ========================================
echo Step 6: Creating Launcher Scripts
echo ========================================

REM Create main launcher script
echo Creating main launcher script...
(
echo @echo off
echo echo ========================================
echo echo VolvoFlashWR Real Hardware Mode
echo echo ========================================
echo echo.
echo echo Starting application for real Vocom hardware testing...
echo echo.
echo.
echo REM Change to application directory
echo cd /d "%%~dp0Application"
echo.
echo REM Start the application
echo if exist "VolvoFlashWR.Launcher.exe" ^(
echo     echo Starting VolvoFlashWR for real hardware...
echo     start "" "VolvoFlashWR.Launcher.exe" --mode=normal --hardware=real
echo     echo Application started successfully!
echo     echo Check the application window for connection status.
echo ^) else ^(
echo     echo ERROR: VolvoFlashWR.Launcher.exe not found!
echo     echo Please ensure the export was completed successfully.
echo     pause
echo ^)
echo.
echo echo.
echo echo Press any key to exit...
echo pause ^>nul
) > "%EXPORT_DIR_FULL%\Scripts\Start_Real_Hardware_Mode.bat"

REM Create system check script
echo Creating system check script...
(
echo @echo off
echo echo ========================================
echo echo VolvoFlashWR System Requirements Check
echo echo ========================================
echo echo.
echo echo Checking system requirements for real hardware...
echo echo.
echo.
echo REM Check for .NET 8.0
echo echo Checking .NET 8.0 installation...
echo dotnet --version ^| findstr "8." ^>nul
echo if %%ERRORLEVEL%% equ 0 ^(
echo     echo ✓ .NET 8.0 is installed
echo ^) else ^(
echo     echo ✗ .NET 8.0 is NOT installed
echo     echo Please install .NET 8.0 Runtime from: https://dotnet.microsoft.com/download/dotnet/8.0
echo ^)
echo echo.
echo.
echo REM Check for Vocom driver
echo echo Checking Vocom driver installation...
echo if exist "C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll" ^(
echo     echo ✓ Vocom driver is installed
echo ^) else ^(
echo     echo ✗ Vocom driver is NOT installed
echo     echo Please install the Vocom 1 adapter driver (CommunicationUnitInstaller-*******.msi^)
echo ^)
echo echo.
echo.
echo REM Check for Phoenix Diag (optional^)
echo echo Checking Phoenix Diag installation (optional^)...
echo if exist "C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021" ^(
echo     echo ✓ Phoenix Diag is installed
echo ^) else ^(
echo     echo ⚠ Phoenix Diag is not installed (optional for advanced features^)
echo ^)
echo echo.
echo.
echo echo System check completed!
echo echo.
echo pause
) > "%EXPORT_DIR_FULL%\Scripts\Check_System_Requirements.bat"

echo Launcher scripts created successfully!

echo.
echo ========================================
echo Step 7: Creating Documentation
echo ========================================

REM Create README for the export
echo Creating README file...
(
echo # VolvoFlashWR Real Hardware Export Package
echo.
echo This package contains everything needed to run VolvoFlashWR with real Vocom hardware.
echo.
echo ## System Requirements
echo.
echo 1. **Windows 10/11 x64**
echo 2. **.NET 8.0 Runtime** - Download from: https://dotnet.microsoft.com/download/dotnet/8.0
echo 3. **Vocom 1 Adapter Driver** - Install CommunicationUnitInstaller-*******.msi
echo 4. **Physical Vocom 1 Adapter** connected via USB/Bluetooth
echo 5. **Real ECU** connected to the Vocom adapter
echo.
echo ## Quick Start
echo.
echo 1. **Check System Requirements**
echo    - Run `Scripts\Check_System_Requirements.bat`
echo    - Install any missing components
echo.
echo 2. **Connect Hardware**
echo    - Connect Vocom 1 adapter to computer
echo    - Connect ECU to Vocom adapter
echo    - Ensure PTT application is NOT running
echo.
echo 3. **Start Application**
echo    - Run `Scripts\Start_Real_Hardware_Mode.bat`
echo    - Application will automatically detect and connect to Vocom
echo.
echo ## Troubleshooting
echo.
echo ### Application doesn't start
echo - Ensure .NET 8.0 Runtime is installed
echo - Check Windows Event Viewer for errors
echo - Run from command line to see error messages
echo.
echo ### Vocom not detected
echo - Verify Vocom driver installation
echo - Check Device Manager for Vocom adapter
echo - Ensure PTT application is closed
echo - Try different USB port
echo.
echo ### ECU communication fails
echo - Verify ECU is powered and connected
echo - Check CAN bus termination
echo - Verify ECU protocol settings
echo - Check application logs in Application\Logs folder
echo.
echo ## File Structure
echo.
echo ```
echo VolvoFlashWR_RealHardware_Export/
echo ├── Application/           # Main application files
echo ├── Libraries/            # Required libraries and drivers
echo ├── Drivers/              # Vocom and ECU driver configurations
echo ├── Documentation/        # MC9S12XEP100 specifications
echo ├── Scripts/              # Launcher and utility scripts
echo └── README.md             # This file
echo ```
echo.
echo ## Support
echo.
echo For technical support, check the application logs in:
echo `Application\Logs\Log_YYYYMMDD_HHMMSS.log`
echo.
echo The application includes comprehensive logging for troubleshooting.
) > "%EXPORT_DIR_FULL%\README.md"

REM Copy important documentation files
echo Copying documentation files...
if exist "README.md" copy "README.md" "%EXPORT_DIR_FULL%\Documentation\Original_README.md"
if exist "REAL_HARDWARE_SETUP_COMPLETE.md" copy "REAL_HARDWARE_SETUP_COMPLETE.md" "%EXPORT_DIR_FULL%\Documentation\"
if exist "HOW_TO_RUN_REAL_HARDWARE.md" copy "HOW_TO_RUN_REAL_HARDWARE.md" "%EXPORT_DIR_FULL%\Documentation\"
if exist "PHOENIX_APCI_REAL_HARDWARE_GUIDE.md" copy "PHOENIX_APCI_REAL_HARDWARE_GUIDE.md" "%EXPORT_DIR_FULL%\Documentation\"

echo Documentation created successfully!

echo.
echo ========================================
echo Step 8: Creating Installation Script
echo ========================================

REM Create installation script for the target system
echo Creating installation script...
(
echo @echo off
echo setlocal enabledelayedexpansion
echo.
echo echo ========================================
echo echo VolvoFlashWR Real Hardware Installation
echo echo ========================================
echo echo.
echo echo This script will set up VolvoFlashWR for real hardware use.
echo echo.
echo.
echo REM Check if running as administrator
echo net session ^>nul 2^>^&1
echo if %%ERRORLEVEL%% neq 0 ^(
echo     echo This script requires administrator privileges.
echo     echo Please run as administrator.
echo     pause
echo     exit /b 1
echo ^)
echo.
echo echo Installing VolvoFlashWR Real Hardware Mode...
echo echo.
echo.
echo REM Create application directory
echo set INSTALL_DIR=C:\VolvoFlashWR
echo if not exist "%%INSTALL_DIR%%" mkdir "%%INSTALL_DIR%%"
echo.
echo REM Copy application files
echo echo Copying application files...
echo xcopy "Application\*" "%%INSTALL_DIR%%\" /E /I /Y
echo.
echo REM Copy libraries to system location
echo echo Installing libraries...
echo if not exist "%%INSTALL_DIR%%\Libraries" mkdir "%%INSTALL_DIR%%\Libraries"
echo xcopy "Libraries\*" "%%INSTALL_DIR%%\Libraries\" /E /I /Y
echo.
echo REM Create desktop shortcut
echo echo Creating desktop shortcut...
echo set SHORTCUT_PATH=%%USERPROFILE%%\Desktop\VolvoFlashWR Real Hardware.lnk
echo powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%%SHORTCUT_PATH%%'); $Shortcut.TargetPath = '%%INSTALL_DIR%%\VolvoFlashWR.Launcher.exe'; $Shortcut.Arguments = '--mode=normal --hardware=real'; $Shortcut.WorkingDirectory = '%%INSTALL_DIR%%'; $Shortcut.IconLocation = '%%INSTALL_DIR%%\VolvoFlashWR.ico'; $Shortcut.Description = 'VolvoFlashWR Real Hardware Mode'; $Shortcut.Save()"
echo.
echo echo Installation completed successfully!
echo echo.
echo echo You can now run VolvoFlashWR from:
echo echo - Desktop shortcut: VolvoFlashWR Real Hardware
echo echo - Start menu: Search for "VolvoFlashWR"
echo echo - Direct path: %%INSTALL_DIR%%\VolvoFlashWR.Launcher.exe
echo echo.
echo pause
) > "%EXPORT_DIR_FULL%\Install.bat"

echo Installation script created successfully!

echo.
echo ========================================
echo Step 9: Final Verification and Packaging
echo ========================================

REM Verify all critical files are present
echo Verifying export package...
set VERIFICATION_FAILED=0

if not exist "%EXPORT_DIR_FULL%\Application\VolvoFlashWR.Launcher.exe" (
    echo ERROR: Main executable not found!
    set VERIFICATION_FAILED=1
)

if not exist "%EXPORT_DIR_FULL%\Libraries\WUDFPuma.dll" (
    echo ERROR: Vocom driver library not found!
    set VERIFICATION_FAILED=1
)

if not exist "%EXPORT_DIR_FULL%\Libraries\apci.dll" (
    echo ERROR: APCI library not found!
    set VERIFICATION_FAILED=1
)

if not exist "%EXPORT_DIR_FULL%\Scripts\Start_Real_Hardware_Mode.bat" (
    echo ERROR: Launcher script not found!
    set VERIFICATION_FAILED=1
)

if %VERIFICATION_FAILED% equ 1 (
    echo.
    echo EXPORT FAILED: Critical files are missing!
    pause
    exit /b 1
)

echo ✓ All critical files verified successfully!

REM Create a compressed archive (if 7-Zip is available)
echo.
echo Creating compressed archive...
where 7z >nul 2>nul
if %ERRORLEVEL% equ 0 (
    echo Creating 7-Zip archive...
    7z a -t7z "%EXPORT_DIR_FULL%.7z" "%EXPORT_DIR_FULL%\*" -mx=9
    echo ✓ Archive created: %EXPORT_DIR_FULL%.7z
) else (
    echo 7-Zip not found, skipping archive creation.
    echo You can manually compress the folder: %EXPORT_DIR_FULL%
)

echo.
echo ========================================
echo EXPORT COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Export package created at: %EXPORT_DIR_FULL%
echo.
echo Package contents:
echo ✓ Application files (Release build, x64)
echo ✓ All required libraries and drivers
echo ✓ MC9S12XEP100 documentation
echo ✓ Launcher scripts for easy startup
echo ✓ System requirements checker
echo ✓ Installation script
echo ✓ Comprehensive documentation
echo.
echo To use on another laptop:
echo 1. Copy the entire folder: %EXPORT_DIR_FULL%
echo 2. Run: Scripts\Check_System_Requirements.bat
echo 3. Install any missing requirements
echo 4. Run: Scripts\Start_Real_Hardware_Mode.bat
echo.
echo The package is ready for real Vocom hardware testing!
echo.
pause
