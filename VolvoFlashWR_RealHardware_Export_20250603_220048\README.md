# VolvoFlashWR Real Hardware Export Package

This package contains everything needed to run VolvoFlashWR with real Vocom hardware.

## System Requirements

1. **Windows 10/11 x64**
2. **.NET 8.0 Runtime** - Download from: https://dotnet.microsoft.com/download/dotnet/8.0
3. **Vocom 1 Adapter Driver** - Install CommunicationUnitInstaller-*******.msi
4. **Physical Vocom 1 Adapter** connected via USB/Bluetooth
5. **Real ECU** connected to the Vocom adapter

## Quick Start

1. **Check System Requirements**
   - Run `Scripts\Check_System_Requirements.bat`
   - Install any missing components

2. **Connect Hardware**
   - Connect Vocom 1 adapter to computer
   - Connect ECU to Vocom adapter
   - Ensure PTT application is NOT running

3. **Start Application**
   - Run `Scripts\Start_Real_Hardware_Mode.bat`
   - Application will automatically detect and connect to Vocom

## Troubleshooting

### Application doesn't start
- Ensure .NET 8.0 Runtime is installed
- Check Windows Event Viewer for errors
- Run from command line to see error messages

### Vocom not detected
- Verify Vocom driver installation
- Check Device Manager for Vocom adapter
- Ensure PTT application is closed
- Try different USB port

### ECU communication fails
- Verify ECU is powered and connected
- Check CAN bus termination
- Verify ECU protocol settings
- Check application logs in Application\Logs folder

## File Structure

```
VolvoFlashWR_RealHardware_Export/
├── Application/           # Main application files
├── Libraries/            # Required libraries and drivers
├── Drivers/              # Vocom and ECU driver configurations
├── Documentation/        # MC9S12XEP100 specifications
├── Scripts/              # Launcher and utility scripts
└── README.md             # This file
```

## Support

For technical support, check the application logs in:
`Application\Logs\Log_YYYYMMDD_HHMMSS.log`

The application includes comprehensive logging for troubleshooting.
